<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="DiagnosticOTP.aspx.vb" Inherits="SPMJ.DiagnosticOTP" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>OTP System Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
        .debug { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; font-family: monospace; white-space: pre-wrap; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>🔍 OTP System Diagnostic Tool</h1>
            <p>This tool helps diagnose OTP microservice connectivity issues.</p>
            
            <div class="test-section">
                <h3>1. Basic Connectivity Test</h3>
                <asp:Button ID="btnTestConnectivity" runat="server" Text="Test Connectivity" CssClass="btn btn-primary" />
                <div id="connectivityResult">
                    <asp:Literal ID="litConnectivityResult" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>2. EmailServiceClient Initialization</h3>
                <asp:Button ID="btnTestInitialization" runat="server" Text="Test Initialization" CssClass="btn btn-primary" />
                <div id="initResult">
                    <asp:Literal ID="litInitResult" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>3. Health Check Tests</h3>
                <asp:Button ID="btnTestHealthChecks" runat="server" Text="Test Health Checks" CssClass="btn btn-primary" />
                <div id="healthResult">
                    <asp:Literal ID="litHealthResult" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>4. Configuration Check</h3>
                <asp:Button ID="btnTestConfig" runat="server" Text="Check Configuration" CssClass="btn btn-success" />
                <div id="configResult">
                    <asp:Literal ID="litConfigResult" runat="server"></asp:Literal>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📋 Debug Output</h3>
                <div class="debug">
                    <asp:Literal ID="litDebugOutput" runat="server"></asp:Literal>
                </div>
            </div>
        </div>
    </form>
</body>
</html>
