# Test Health Endpoint Response
Write-Host "Testing Email Service Health Endpoint..." -ForegroundColor Cyan

# Test 1: Without API Key
Write-Host "`n1. Testing without API key..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -Method GET -UseBasicParsing
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content: $($response.Content)" -ForegroundColor White

    # Test if content contains "healthy"
    $containsHealthy = $response.Content.Contains("healthy")
    Write-Host "Contains 'healthy': $containsHealthy" -ForegroundColor Yellow

} catch {
    Write-Host "Health check without API key failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: With API Key
Write-Host "`n2. Testing with API key..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    }
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content: $($response.Content)" -ForegroundColor White

    # Test if content contains "healthy"
    $containsHealthy = $response.Content.Contains("healthy")
    Write-Host "Contains 'healthy': $containsHealthy" -ForegroundColor Yellow

    # Test JSON parsing
    try {
        $jsonResponse = $response.Content | ConvertFrom-Json
        Write-Host "JSON Status: $($jsonResponse.status)" -ForegroundColor Cyan
    } catch {
        Write-Host "JSON parsing failed: $($_.Exception.Message)" -ForegroundColor Red
    }

} catch {
    Write-Host "Health check with API key failed: $($_.Exception.Message)" -ForegroundColor Red
}
