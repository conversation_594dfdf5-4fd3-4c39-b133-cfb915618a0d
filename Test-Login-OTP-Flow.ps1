# Test Login OTP Flow
Write-Host "Testing Login OTP Flow with Enhanced Health Check..." -ForegroundColor Cyan

# Test 1: Verify microservice is running
Write-Host "`n1. Verifying microservice status..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Microservice is running" -ForegroundColor Green
    Write-Host "   Status: $($healthResponse.status)" -ForegroundColor White
    Write-Host "   Timestamp: $($healthResponse.timestamp)" -ForegroundColor White
} catch {
    Write-Host "❌ Microservice is not running: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please start the microservice first:" -ForegroundColor Yellow
    Write-Host "   cd 'SPMJ.EmailService'" -ForegroundColor Gray
    Write-Host "   dotnet run" -ForegroundColor Gray
    exit 1
}

# Test 2: Test health check with API key
Write-Host "`n2. Testing health check with API key..." -ForegroundColor Yellow
try {
    $headers = @{
        "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    }
    $response = Invoke-WebRequest -Uri "http://localhost:5000/health" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "✅ Health check with API key: SUCCESS" -ForegroundColor Green
    Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   Content: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "❌ Health check with API key: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test OTP generation
Write-Host "`n3. Testing OTP generation..." -ForegroundColor Yellow
try {
    $otpRequest = @{
        UserId = "testuser"
        Email = "<EMAIL>"
        Purpose = "LOGIN"
    } | ConvertTo-Json

    $headers = @{
        "Content-Type" = "application/json"
        "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
    }

    $otpResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/otp/generate" -Method POST -Body $otpRequest -Headers $headers -TimeoutSec 10
    Write-Host "✅ OTP generation: SUCCESS" -ForegroundColor Green
    Write-Host "   Success: $($otpResponse.Success)" -ForegroundColor White
    Write-Host "   Message: $($otpResponse.Message)" -ForegroundColor White
} catch {
    Write-Host "❌ OTP generation: FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. Next Steps:" -ForegroundColor Yellow
Write-Host "   - Open the test page: http://localhost:8080/SPMJ/TestEmailServiceHealth.aspx" -ForegroundColor Gray
Write-Host "   - Click 'Test Health Check' to see detailed debug output" -ForegroundColor Gray
Write-Host "   - Try logging in to see if OTP verification works" -ForegroundColor Gray
Write-Host "   - Check Visual Studio Debug Output for detailed logs" -ForegroundColor Gray
