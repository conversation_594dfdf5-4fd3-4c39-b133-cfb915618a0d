Imports System.Configuration

Partial Public Class TestOtpConnection
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Page load logic if needed
    End Sub

    Protected Sub btnTestConnection_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestConnection.Click
        Try
            lblResult.Text = "Testing OTP microservice connection...<br/>"
            
            ' Initialize email service client
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            lblResult.Text += "Service URL: " & serviceUrl & "<br/>"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            lblResult.Text += "Email client initialized successfully<br/>"
            
            ' Test health check
            lblResult.Text += "Testing health check...<br/>"
            Dim isHealthy As Boolean = emailClient.CheckHealth()
            lblResult.Text += "Health check result: " & isHealthy.ToString() & "<br/>"
            
            If Not isHealthy Then
                lblResult.Text += "Trying simple health check...<br/>"
                Dim isSimpleHealthy As Boolean = emailClient.CheckHealthSimple()
                lblResult.Text += "Simple health check result: " & isSimpleHealthy.ToString() & "<br/>"
            End If
            
            ' Test OTP generation if health check passes
            If isHealthy Then
                lblResult.Text += "Testing OTP generation...<br/>"
                Dim otpResponse = emailClient.GenerateOTP("TEST_USER", "<EMAIL>", "LOGIN")
                lblResult.Text += "OTP generation success: " & otpResponse.Success.ToString() & "<br/>"
                lblResult.Text += "OTP generation message: " & otpResponse.Message & "<br/>"
            Else
                lblResult.Text += "<span style='color: red;'>Health check failed - cannot test OTP generation</span><br/>"
            End If
            
            lblResult.Text += "<br/><span style='color: green;'>Test completed successfully!</span>"
            
        Catch ex As Exception
            lblResult.Text += "<br/><span style='color: red;'>ERROR: " & ex.Message & "</span><br/>"
            lblResult.Text += "Exception type: " & ex.GetType().Name & "<br/>"
            If ex.InnerException IsNot Nothing Then
                lblResult.Text += "Inner exception: " & ex.InnerException.Message & "<br/>"
            End If
        End Try
    End Sub
End Class
