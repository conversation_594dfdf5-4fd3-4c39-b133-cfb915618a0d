<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="TestEmailServiceHealth.aspx.vb" Inherits="SPMJ.TestEmailServiceHealth" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test Email Service Health</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-left: 3px solid #ccc; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Email Service Health Check Test</h1>
        
        <asp:Button ID="btnTestHealth" runat="server" Text="Test Health Check" />
        <br /><br />
        
        <div>
            <strong>Health Check Result:</strong><br />
            <asp:Literal ID="litHealthResult" runat="server"></asp:Literal>
        </div>
        
        <br />
        
        <div>
            <strong>Debug Information:</strong><br />
            <div class="debug">
                <asp:Literal ID="litDebugInfo" runat="server"></asp:Literal>
            </div>
        </div>
    </form>
</body>
</html>
