Imports System.Configuration

Partial Public Class TestCompleteOtpFlow
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Page load logic if needed
    End Sub

    Protected Sub btnTestHealth_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestHealth.Click
        Try
            lblHealthResult.Text = "Testing OTP microservice health...<br/>"
            
            ' Initialize email service client
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            lblHealthResult.Text += "Service URL: " & serviceUrl & "<br/>"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            lblHealthResult.Text += "Email client initialized<br/>"
            
            ' Test health check
            Dim isHealthy As Boolean = emailClient.CheckHealth()
            lblHealthResult.Text += "Health check result: " & isHealthy.ToString() & "<br/>"
            
            If isHealthy Then
                lblHealthResult.Text += "<span class='success'>✅ OTP microservice is healthy!</span><br/>"
            Else
                lblHealthResult.Text += "<span class='error'>❌ OTP microservice health check failed</span><br/>"
                
                ' Try simple health check
                Dim isSimpleHealthy As Boolean = emailClient.CheckHealthSimple()
                lblHealthResult.Text += "Simple health check result: " & isSimpleHealthy.ToString() & "<br/>"
                
                If isSimpleHealthy Then
                    lblHealthResult.Text += "<span class='warning'>⚠️ Simple health check passed</span><br/>"
                End If
            End If
            
        Catch ex As Exception
            lblHealthResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestGenerate_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestGenerate.Click
        Try
            lblGenerateResult.Text = "Testing OTP generation...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            ' Test OTP generation
            Dim response = emailClient.GenerateOTP(txtTestUserId.Text, txtTestEmail.Text, "LOGIN")
            
            lblGenerateResult.Text += "OTP Generation Result:<br/>"
            lblGenerateResult.Text += "Success: " & response.Success.ToString() & "<br/>"
            lblGenerateResult.Text += "Message: " & response.Message & "<br/>"
            
            If response.Success Then
                lblGenerateResult.Text += "<span class='success'>✅ OTP generated successfully!</span><br/>"
                lblGenerateResult.Text += "<span class='info'>📧 Check the email or use a test OTP code for validation</span><br/>"
            Else
                lblGenerateResult.Text += "<span class='error'>❌ OTP generation failed</span><br/>"
            End If
            
        Catch ex As Exception
            lblGenerateResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestValidate_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestValidate.Click
        Try
            lblValidateResult.Text = "Testing OTP validation...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            ' Test OTP validation
            Dim response = emailClient.ValidateOTP(txtValidateUserId.Text, txtOtpCode.Text, "LOGIN")
            
            lblValidateResult.Text += "OTP Validation Result:<br/>"
            lblValidateResult.Text += "Success: " & response.Success.ToString() & "<br/>"
            lblValidateResult.Text += "Message: " & response.Message & "<br/>"
            
            If response.Success Then
                lblValidateResult.Text += "<span class='success'>✅ OTP validation successful!</span><br/>"
            Else
                lblValidateResult.Text += "<span class='error'>❌ OTP validation failed</span><br/>"
                lblValidateResult.Text += "<span class='info'>💡 Try generating a new OTP first, then use the correct code</span><br/>"
            End If
            
        Catch ex As Exception
            lblValidateResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestVerificationPage_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestVerificationPage.Click
        Try
            lblVerificationResult.Text = "Testing OTP verification page logic...<br/>"
            
            ' Simulate the logic from OtpVerification.aspx.vb
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            lblVerificationResult.Text += "Service URL: " & serviceUrl & "<br/>"
            
            ' Test email service client initialization
            Dim emailServiceClient As EmailServiceClient = Nothing
            Try
                emailServiceClient = New EmailServiceClient(serviceUrl)
                lblVerificationResult.Text += "<span class='success'>✅ Email service client initialized</span><br/>"
            Catch initEx As Exception
                lblVerificationResult.Text += "<span class='error'>❌ Email service client initialization failed: " & initEx.Message & "</span><br/>"
                Return
            End Try
            
            ' Test health check (same as OtpVerification page does)
            If emailServiceClient IsNot Nothing Then
                Dim isHealthy As Boolean = False
                Try
                    isHealthy = emailServiceClient.CheckHealth()
                    lblVerificationResult.Text += "Primary health check: " & isHealthy.ToString() & "<br/>"
                    
                    If Not isHealthy Then
                        isHealthy = emailServiceClient.CheckHealthSimple()
                        lblVerificationResult.Text += "Simple health check: " & isHealthy.ToString() & "<br/>"
                    End If
                Catch healthEx As Exception
                    lblVerificationResult.Text += "<span class='error'>Health check exception: " & healthEx.Message & "</span><br/>"
                End Try
                
                If isHealthy Then
                    lblVerificationResult.Text += "<span class='success'>✅ OTP verification page would work correctly</span><br/>"
                Else
                    lblVerificationResult.Text += "<span class='error'>❌ OTP verification page would show 'Sistem OTP tidak tersedia'</span><br/>"
                End If
            Else
                lblVerificationResult.Text += "<span class='error'>❌ Email service client is Nothing</span><br/>"
            End If
            
        Catch ex As Exception
            lblVerificationResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnSetupSession_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSetupSession.Click
        Try
            ' Setup session variables as if user just logged in
            Session("TEMP_USER_ID") = "testuser"
            Session("TEMP_MODUL") = "ALL"
            Session("TEMP_AKSES") = "USER"
            Session("TEMP_ORIGIN") = "yes"
            Session("USER_EMAIL") = "<EMAIL>"
            
            lblSessionResult.Text = "<span class='success'>✅ Test session setup complete</span><br/>"
            lblSessionResult.Text += "TEMP_USER_ID: " & Session("TEMP_USER_ID").ToString() & "<br/>"
            lblSessionResult.Text += "USER_EMAIL: " & Session("USER_EMAIL").ToString() & "<br/>"
            lblSessionResult.Text += "<span class='info'>💡 You can now test the OTP verification page</span><br/>"
            
        Catch ex As Exception
            lblSessionResult.Text = "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnClearSession_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnClearSession.Click
        Try
            Session.Clear()
            lblSessionResult.Text = "<span class='info'>🧹 Session cleared</span><br/>"
        Catch ex As Exception
            lblSessionResult.Text = "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnGoToLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToLogin.Click
        Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub btnGoToOtpVerification_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToOtpVerification.Click
        If Session("TEMP_USER_ID") Is Nothing Then
            lblNavigationResult.Text = "<span class='warning'>⚠️ No session setup. Click 'Setup Test Session' first.</span><br/>"
        Else
            Response.Redirect("OtpVerification.aspx")
        End If
    End Sub
End Class
