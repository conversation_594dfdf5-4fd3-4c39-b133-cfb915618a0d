Imports System.Data.OleDb

Partial Public Class AddTestUser
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Page load logic if needed
    End Sub

    Protected Sub btnAddUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnAddUser.Click
        Try
            lblResult.Text = "Adding test user...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Check if user already exists
            Cmd.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE id_pg = ?"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@id_pg", txtUserId.Text.Trim())
            
            Dim userExists As Integer = CInt(Cmd.ExecuteScalar())
            
            If userExists > 0 Then
                lblResult.Text += "<span style='color: orange;'>User already exists. Use 'Update Existing User Email' instead.</span><br/>"
                Cn.Close()
                Return
            End If
            
            ' Create encrypted password
            Dim passwordHelper As New PasswordHelper()
            Dim salt As String = passwordHelper.GenerateSalt()
            Dim hashedPassword As String = passwordHelper.HashPassword(txtPassword.Text, salt)
            
            ' Insert new user
            Cmd.CommandText = "INSERT INTO pn_pengguna (id_pg, pwd, salt, password_migrated, email, nama, status, modul, akses) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@id_pg", txtUserId.Text.Trim())
            Cmd.Parameters.AddWithValue("@pwd", hashedPassword)
            Cmd.Parameters.AddWithValue("@salt", salt)
            Cmd.Parameters.AddWithValue("@password_migrated", True)
            Cmd.Parameters.AddWithValue("@email", txtEmail.Text.Trim())
            Cmd.Parameters.AddWithValue("@nama", txtName.Text.Trim())
            Cmd.Parameters.AddWithValue("@status", 1)
            Cmd.Parameters.AddWithValue("@modul", "ALL")
            Cmd.Parameters.AddWithValue("@akses", "USER")
            
            Dim rowsAffected As Integer = Cmd.ExecuteNonQuery()
            
            If rowsAffected > 0 Then
                lblResult.Text += "<span style='color: green;'>Test user added successfully!</span><br/>"
                lblResult.Text += "User ID: " & txtUserId.Text & "<br/>"
                lblResult.Text += "Email: " & txtEmail.Text & "<br/>"
                lblResult.Text += "Password: " & txtPassword.Text & "<br/>"
                lblResult.Text += "Password is encrypted and migrated: Yes<br/>"
            Else
                lblResult.Text += "<span style='color: red;'>Failed to add user.</span><br/>"
            End If
            
            Cn.Close()
            
        Catch ex As Exception
            lblResult.Text += "<br/><span style='color: red;'>ERROR: " & ex.Message & "</span><br/>"
            lblResult.Text += "Exception type: " & ex.GetType().Name & "<br/>"
            If ex.InnerException IsNot Nothing Then
                lblResult.Text += "Inner exception: " & ex.InnerException.Message & "<br/>"
            End If
        End Try
    End Sub

    Protected Sub btnUpdateExisting_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnUpdateExisting.Click
        Try
            lblResult.Text = "Updating existing user email...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Update existing user's email
            Cmd.CommandText = "UPDATE pn_pengguna SET email = ? WHERE id_pg = ? AND status = 1"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@email", txtEmail.Text.Trim())
            Cmd.Parameters.AddWithValue("@id_pg", txtUserId.Text.Trim())
            
            Dim rowsAffected As Integer = Cmd.ExecuteNonQuery()
            
            If rowsAffected > 0 Then
                lblResult.Text += "<span style='color: green;'>User email updated successfully!</span><br/>"
                lblResult.Text += "User ID: " & txtUserId.Text & "<br/>"
                lblResult.Text += "New Email: " & txtEmail.Text & "<br/>"
            Else
                lblResult.Text += "<span style='color: red;'>User not found or update failed.</span><br/>"
            End If
            
            Cn.Close()
            
        Catch ex As Exception
            lblResult.Text += "<br/><span style='color: red;'>ERROR: " & ex.Message & "</span><br/>"
            lblResult.Text += "Exception type: " & ex.GetType().Name & "<br/>"
            If ex.InnerException IsNot Nothing Then
                lblResult.Text += "Inner exception: " & ex.InnerException.Message & "<br/>"
            End If
        End Try
    End Sub
End Class
