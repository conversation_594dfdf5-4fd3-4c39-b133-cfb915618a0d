Imports System.Data.OleDb
Imports System.Configuration

Partial Public Class TestKolejLoginEnhancements
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            lblOtpHealthResult.Text = "Click 'Test OTP Service' to check microservice status."
        End If
    End Sub

    Protected Sub btnTestOtpHealth_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestOtpHealth.Click
        Try
            lblOtpHealthResult.Text = "Testing OTP microservice health...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            lblOtpHealthResult.Text += "Service URL: " & serviceUrl & "<br/>"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            lblOtpHealthResult.Text += "Email client initialized<br/>"
            
            Dim isHealthy As Boolean = emailClient.CheckHealth()
            lblOtpHealthResult.Text += "Health check result: " & isHealthy.ToString() & "<br/>"
            
            If isHealthy Then
                lblOtpHealthResult.Text += "<span class='success'>✅ OTP microservice is healthy and ready for KOLEJ-PDSA!</span><br/>"
            Else
                lblOtpHealthResult.Text += "<span class='error'>❌ OTP microservice health check failed</span><br/>"
            End If
            
        Catch ex As Exception
            lblOtpHealthResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestDatabase_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestDatabase.Click
        Try
            lblDatabaseResult.Text = "Checking kj_pengguna table schema...<br/>"
            
            Dim Cn As New OleDbConnection
            Cn.ConnectionString = ServerId
            Cn.Open()
            
            ' Check if required columns exist
            Dim requiredColumns As String() = {"email", "salt", "pwd_encrypted", "failed_login_attempts", "account_locked"}
            Dim existingColumns As New List(Of String)
            
            For Each column As String In requiredColumns
                Try
                    Dim Cmd As New OleDbCommand
                    Cmd.Connection = Cn
                    Cmd.CommandText = "SELECT TOP 1 " & column & " FROM kj_pengguna"
                    Cmd.ExecuteScalar()
                    existingColumns.Add(column)
                Catch
                    ' Column doesn't exist
                End Try
            Next
            
            lblDatabaseResult.Text += "<table><tr><th>Column</th><th>Status</th></tr>"
            For Each column As String In requiredColumns
                If existingColumns.Contains(column) Then
                    lblDatabaseResult.Text += "<tr><td>" & column & "</td><td><span class='success'>✅ EXISTS</span></td></tr>"
                Else
                    lblDatabaseResult.Text += "<tr><td>" & column & "</td><td><span class='error'>❌ MISSING</span></td></tr>"
                End If
            Next
            lblDatabaseResult.Text += "</table>"
            
            ' Check total users
            Dim Cmd2 As New OleDbCommand
            Cmd2.Connection = Cn
            Cmd2.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE status = 1"
            Dim userCount = Cmd2.ExecuteScalar()
            lblDatabaseResult.Text += "<br/>Active KOLEJ users: " & userCount.ToString()
            
            Cn.Close()
            
        Catch ex As Exception
            lblDatabaseResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestUsers_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestUsers.Click
        Try
            lblUsersResult.Text = "Checking KOLEJ users...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            Dim Rdr As OleDbDataReader
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            Cmd.CommandText = "SELECT TOP 10 kp.Id_PG, kp.email, kp.pwd_encrypted, kp.failed_login_attempts, pk.Dc_KOLEJ " &
                             "FROM kj_pengguna kp LEFT JOIN pn_kolej pk ON kp.Id_KOLEJ = pk.Id_KOLEJ " &
                             "WHERE kp.status = 1 ORDER BY kp.Id_PG"
            Rdr = Cmd.ExecuteReader()
            
            lblUsersResult.Text += "<table><tr><th>User ID</th><th>Email</th><th>Encrypted</th><th>Failed Attempts</th><th>College</th><th>OTP Ready</th></tr>"
            
            While Rdr.Read()
                Dim userId As String = If(IsDBNull(Rdr("Id_PG")), "", Rdr("Id_PG").ToString())
                Dim email As String = If(IsDBNull(Rdr("email")), "", Rdr("email").ToString())
                Dim isEncrypted As Boolean = If(IsDBNull(Rdr("pwd_encrypted")), False, CBool(Rdr("pwd_encrypted")))
                Dim failedAttempts As Integer = If(IsDBNull(Rdr("failed_login_attempts")), 0, CInt(Rdr("failed_login_attempts")))
                Dim college As String = If(IsDBNull(Rdr("Dc_KOLEJ")), "", Rdr("Dc_KOLEJ").ToString())
                
                Dim otpReady As Boolean = Not String.IsNullOrEmpty(email) AndAlso email.Contains("@")
                
                lblUsersResult.Text += "<tr>"
                lblUsersResult.Text += "<td>" & userId & "</td>"
                lblUsersResult.Text += "<td>" & email & "</td>"
                lblUsersResult.Text += "<td>" & If(isEncrypted, "✅", "❌") & "</td>"
                lblUsersResult.Text += "<td>" & failedAttempts.ToString() & "</td>"
                lblUsersResult.Text += "<td>" & college & "</td>"
                lblUsersResult.Text += "<td style='color: " & If(otpReady, "green", "red") & ";'>" & If(otpReady, "✅", "❌") & "</td>"
                lblUsersResult.Text += "</tr>"
            End While
            
            lblUsersResult.Text += "</table>"
            Rdr.Close()
            Cn.Close()
            
        Catch ex As Exception
            lblUsersResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestOtpGeneration_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestOtpGeneration.Click
        Try
            lblEmailServiceResult.Text = "Testing OTP generation for KOLEJ system...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            Dim response = emailClient.GenerateOTP(txtTestUserId.Text, txtTestEmail.Text, "LOGIN_KOLEJ_SECURE")
            
            lblEmailServiceResult.Text += "OTP Generation Result:<br/>"
            lblEmailServiceResult.Text += "Success: " & response.Success.ToString() & "<br/>"
            lblEmailServiceResult.Text += "Message: " & response.Message & "<br/>"
            
            If response.Success Then
                lblEmailServiceResult.Text += "<span class='success'>✅ OTP generated successfully for KOLEJ system!</span><br/>"
            Else
                lblEmailServiceResult.Text += "<span class='error'>❌ OTP generation failed</span><br/>"
            End If
            
        Catch ex As Exception
            lblEmailServiceResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnTestPasswordReset_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestPasswordReset.Click
        Try
            lblEmailServiceResult.Text = "Testing password reset for KOLEJ system...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            Dim response = emailClient.GenerateTemporaryPassword(txtTestUserId.Text, txtTestEmail.Text, "KOLEJ_PASSWORD_RESET")
            
            lblEmailServiceResult.Text += "Password Reset Result:<br/>"
            lblEmailServiceResult.Text += "Success: " & response.Success.ToString() & "<br/>"
            lblEmailServiceResult.Text += "Message: " & response.Message & "<br/>"
            
            If response.Success Then
                lblEmailServiceResult.Text += "<span class='success'>✅ Password reset email sent successfully for KOLEJ system!</span><br/>"
            Else
                lblEmailServiceResult.Text += "<span class='error'>❌ Password reset failed</span><br/>"
            End If
            
        Catch ex As Exception
            lblEmailServiceResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnAddTestUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnAddTestUser.Click
        Try
            lblUserManagementResult.Text = "Adding test KOLEJ user...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Check if user exists
            Cmd.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE Id_PG = ?"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@Id_PG", txtTestUserId.Text)
            
            Dim userExists As Integer = CInt(Cmd.ExecuteScalar())
            
            If userExists > 0 Then
                lblUserManagementResult.Text += "<span class='warning'>⚠️ User already exists</span><br/>"
            Else
                ' Create encrypted password
                Dim passwordHelper As New PasswordHelper()
                Dim salt As String = passwordHelper.GenerateSalt()
                Dim hashedPassword As String = passwordHelper.HashPassword("kolej123", salt)
                
                ' Insert new KOLEJ user
                Cmd.CommandText = "INSERT INTO kj_pengguna (Id_PG, PWD, salt, pwd_encrypted, email, NAMA, STATUS, Id_KOLEJ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                Cmd.Parameters.Clear()
                Cmd.Parameters.AddWithValue("@Id_PG", txtTestUserId.Text)
                Cmd.Parameters.AddWithValue("@PWD", hashedPassword)
                Cmd.Parameters.AddWithValue("@salt", salt)
                Cmd.Parameters.AddWithValue("@pwd_encrypted", True)
                Cmd.Parameters.AddWithValue("@email", txtTestEmail.Text)
                Cmd.Parameters.AddWithValue("@NAMA", "Test KOLEJ User")
                Cmd.Parameters.AddWithValue("@STATUS", 1)
                Cmd.Parameters.AddWithValue("@Id_KOLEJ", "K001") ' Default college ID
                
                Dim rowsAffected As Integer = Cmd.ExecuteNonQuery()
                
                If rowsAffected > 0 Then
                    lblUserManagementResult.Text += "<span class='success'>✅ Test KOLEJ user created successfully!</span><br/>"
                    lblUserManagementResult.Text += "User ID: " & txtTestUserId.Text & "<br/>"
                    lblUserManagementResult.Text += "Password: kolej123<br/>"
                    lblUserManagementResult.Text += "Email: " & txtTestEmail.Text & "<br/>"
                    lblUserManagementResult.Text += "Encrypted: Yes<br/>"
                End If
            End If
            
            Cn.Close()
            
        Catch ex As Exception
            lblUserManagementResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnUpdateUserEmail_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnUpdateUserEmail.Click
        Try
            lblUserManagementResult.Text = "Updating user email...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            Cmd.CommandText = "UPDATE kj_pengguna SET email = ? WHERE Id_PG = ? AND status = 1"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@email", txtTestEmail.Text)
            Cmd.Parameters.AddWithValue("@Id_PG", txtTestUserId.Text)
            
            Dim rowsAffected As Integer = Cmd.ExecuteNonQuery()
            
            If rowsAffected > 0 Then
                lblUserManagementResult.Text += "<span class='success'>✅ User email updated successfully!</span><br/>"
                lblUserManagementResult.Text += "User ID: " & txtTestUserId.Text & "<br/>"
                lblUserManagementResult.Text += "New Email: " & txtTestEmail.Text & "<br/>"
            Else
                lblUserManagementResult.Text += "<span class='error'>❌ User not found or update failed</span><br/>"
            End If
            
            Cn.Close()
            
        Catch ex As Exception
            lblUserManagementResult.Text += "<span class='error'>ERROR: " & ex.Message & "</span><br/>"
        End Try
    End Sub

    Protected Sub btnGoToKolejLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToKolejLogin.Click
        Response.Redirect("Login_J.aspx")
    End Sub

    Protected Sub btnGoToOtpVerification_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToOtpVerification.Click
        ' Setup test session for OTP verification
        Session("TEMP_USER_ID") = txtTestUserId.Text
        Session("TEMP_SYSTEM_TYPE") = "KOLEJ-PDSA"
        Session("TEMP_EMAIL") = txtTestEmail.Text
        
        lblNavigationResult.Text = "<span class='info'>🔄 Test session setup for OTP verification</span><br/>"
        Response.Redirect("OtpVerification.aspx")
    End Sub
End Class
