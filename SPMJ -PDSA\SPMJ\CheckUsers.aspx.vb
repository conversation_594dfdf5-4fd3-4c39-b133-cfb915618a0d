Imports System.Data.OleDb

Partial Public Class CheckUsers
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Page load logic if needed
    End Sub

    Protected Sub btnCheckUsers_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCheckUsers.Click
        Try
            lblResult.Text = "Checking users in database...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            Dim Rdr As OleDbDataReader
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Check all active users
            Cmd.CommandText = "SELECT id_pg, email, password_migrated, is_temporary FROM pn_pengguna WHERE status = 1 ORDER BY id_pg"
            Rdr = Cmd.ExecuteReader()
            
            lblResult.Text += "<table border='1' style='border-collapse: collapse;'>"
            lblResult.Text += "<tr><th>User ID</th><th>Email</th><th>Password Migrated</th><th>Is Temporary</th><th>OTP Required</th></tr>"
            
            While Rdr.Read()
                Dim userId As String = Rdr("id_pg").ToString()
                Dim email As String = If(IsDBNull(Rdr("email")), "", Rdr("email").ToString())
                Dim passwordMigrated As Boolean = If(IsDBNull(Rdr("password_migrated")), False, CBool(Rdr("password_migrated")))
                Dim isTemporary As Boolean = If(IsDBNull(Rdr("is_temporary")), False, CBool(Rdr("is_temporary")))
                
                ' Determine if OTP would be required
                Dim otpRequired As Boolean = Not String.IsNullOrEmpty(email) AndAlso email.Length > 5 AndAlso email.Contains("@")
                
                lblResult.Text += "<tr>"
                lblResult.Text += "<td>" & userId & "</td>"
                lblResult.Text += "<td>" & email & "</td>"
                lblResult.Text += "<td>" & passwordMigrated.ToString() & "</td>"
                lblResult.Text += "<td>" & isTemporary.ToString() & "</td>"
                lblResult.Text += "<td style='color: " & If(otpRequired, "green", "red") & ";'>" & otpRequired.ToString() & "</td>"
                lblResult.Text += "</tr>"
            End While
            
            lblResult.Text += "</table>"
            Rdr.Close()
            Cn.Close()
            
            lblResult.Text += "<br/><span style='color: green;'>Check completed successfully!</span>"
            
        Catch ex As Exception
            lblResult.Text += "<br/><span style='color: red;'>ERROR: " & ex.Message & "</span><br/>"
            lblResult.Text += "Exception type: " & ex.GetType().Name & "<br/>"
            If ex.InnerException IsNot Nothing Then
                lblResult.Text += "Inner exception: " & ex.InnerException.Message & "<br/>"
            End If
        End Try
    End Sub
End Class
