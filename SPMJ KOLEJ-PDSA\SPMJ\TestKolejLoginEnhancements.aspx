<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="TestKolejLoginEnhancements.aspx.vb" Inherits="SPMJ.TestKolejLoginEnhancements" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Test KOLEJ Login Enhancements</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>SPMJ KOLEJ-PDSA Login Enhancement Test</h1>
        
        <div class="test-section">
            <h3>1. Test OTP Microservice Health</h3>
            <asp:Button ID="btnTestOtpHealth" runat="server" Text="Test OTP Service" OnClick="btnTestOtpHealth_Click" />
            <br /><br />
            <asp:Label ID="lblOtpHealthResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>2. Test Database Schema (kj_pengguna table)</h3>
            <asp:Button ID="btnTestDatabase" runat="server" Text="Check Database Schema" OnClick="btnTestDatabase_Click" />
            <br /><br />
            <asp:Label ID="lblDatabaseResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>3. Test User Data (Check existing users)</h3>
            <asp:Button ID="btnTestUsers" runat="server" Text="Check KOLEJ Users" OnClick="btnTestUsers_Click" />
            <br /><br />
            <asp:Label ID="lblUsersResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>4. Test Email Service Integration</h3>
            User ID: <asp:TextBox ID="txtTestUserId" runat="server" Text="testkolej"></asp:TextBox>
            Email: <asp:TextBox ID="txtTestEmail" runat="server" Text="<EMAIL>"></asp:TextBox>
            <br /><br />
            <asp:Button ID="btnTestOtpGeneration" runat="server" Text="Test OTP Generation" OnClick="btnTestOtpGeneration_Click" />
            <asp:Button ID="btnTestPasswordReset" runat="server" Text="Test Password Reset" OnClick="btnTestPasswordReset_Click" />
            <br /><br />
            <asp:Label ID="lblEmailServiceResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>5. Test User Management</h3>
            <asp:Button ID="btnAddTestUser" runat="server" Text="Add Test KOLEJ User" OnClick="btnAddTestUser_Click" />
            <asp:Button ID="btnUpdateUserEmail" runat="server" Text="Update User Email" OnClick="btnUpdateUserEmail_Click" />
            <br /><br />
            <asp:Label ID="lblUserManagementResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>6. Navigation Tests</h3>
            <asp:Button ID="btnGoToKolejLogin" runat="server" Text="Go to KOLEJ Login" OnClick="btnGoToKolejLogin_Click" />
            <asp:Button ID="btnGoToOtpVerification" runat="server" Text="Go to OTP Verification" OnClick="btnGoToOtpVerification_Click" />
            <br /><br />
            <asp:Label ID="lblNavigationResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>7. Expected Enhancements Summary</h3>
            <ul>
                <li><strong>✅ Encrypted Password Support:</strong> Users with encrypted passwords trigger OTP verification</li>
                <li><strong>✅ OTP Integration:</strong> Seamless integration with email service microservice</li>
                <li><strong>✅ Password Recovery:</strong> "Lupa Kata Laluan?" link with toggle functionality</li>
                <li><strong>✅ Email Field:</strong> kj_pengguna table includes email column for OTP and password reset</li>
                <li><strong>✅ Security Enhancement:</strong> Account locking, failed login tracking</li>
                <li><strong>✅ Successful Login Flow:</strong> OTP verification → blank.aspx redirect</li>
            </ul>
        </div>
    </form>
</body>
</html>
