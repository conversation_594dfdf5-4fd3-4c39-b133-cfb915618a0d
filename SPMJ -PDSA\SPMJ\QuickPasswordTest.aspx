<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="QuickPasswordTest.aspx.vb" Inherits="SPMJ.QuickPasswordTest" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Quick Password Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Quick Password Verification Test</h1>
        
        <div>
            <p>User ID: <asp:TextBox ID="txtUserId" runat="server"></asp:TextBox></p>
            <p>Password: <asp:TextBox ID="txtPassword" runat="server" TextMode="Password"></asp:TextBox></p>
            <asp:Button ID="btnTest" runat="server" Text="Test Password" />
        </div>
        
        <div id="results">
            <asp:Literal ID="litResults" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
