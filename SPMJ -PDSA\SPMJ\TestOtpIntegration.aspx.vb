Imports System.Data.OleDb
Imports System.Configuration

Partial Public Class TestOtpIntegration
    Inherits System.Web.UI.Page

    Private emailServiceClient As EmailServiceClient
    Private debugMessages As New List(Of String)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ' Initialize email service client
            Try
                Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
                If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
                emailServiceClient = New EmailServiceClient(serviceUrl)
                AddDebugMessage("Email service client initialized with URL: " & serviceUrl)
            Catch ex As Exception
                AddDebugMessage("Failed to initialize email service client: " & ex.Message)
            End Try
        End If
    End Sub

    Protected Sub btnTestHealth_Click(sender As Object, e As EventArgs) Handles btnTestHealth.Click
        Try
            If emailServiceClient Is Nothing Then
                litHealthResult.Text = "<div class='error'>❌ Email service client not initialized</div>"
                AddDebugMessage("Health check failed: Client not initialized")
                Return
            End If

            Dim isHealthy As Boolean = emailServiceClient.CheckHealth()
            If isHealthy Then
                litHealthResult.Text = "<div class='success'>✅ Email Microservice is healthy and responding</div>"
                AddDebugMessage("Health check successful")
            Else
                litHealthResult.Text = "<div class='error'>❌ Email Microservice is not responding</div>"
                AddDebugMessage("Health check failed: No response")
            End If

        Catch ex As Exception
            litHealthResult.Text = "<div class='error'>❌ Health check error: " & ex.Message & "</div>"
            AddDebugMessage("Health check exception: " & ex.Message)
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnGenerateOtp_Click(sender As Object, e As EventArgs) Handles btnGenerateOtp.Click
        Try
            If String.IsNullOrEmpty(txtUserId.Text) OrElse String.IsNullOrEmpty(txtEmail.Text) Then
                litOtpGenResult.Text = "<div class='error'>Please enter both User ID and Email</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                litOtpGenResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            Dim response = emailServiceClient.GenerateOTP(txtUserId.Text, txtEmail.Text, "LOGIN")

            If response.Success Then
                litOtpGenResult.Text = "<div class='success'>✅ OTP generated successfully: " & response.Message & "</div>"
                AddDebugMessage("OTP generation successful for user: " & txtUserId.Text)
            Else
                litOtpGenResult.Text = "<div class='error'>❌ OTP generation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP generation failed: " & response.Message)
            End If

        Catch ex As Exception
            litOtpGenResult.Text = "<div class='error'>❌ OTP generation error: " & ex.Message & "</div>"
            AddDebugMessage("OTP generation exception: " & ex.Message)
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnValidateOtp_Click(sender As Object, e As EventArgs) Handles btnValidateOtp.Click
        Try
            If String.IsNullOrEmpty(txtUserId.Text) OrElse String.IsNullOrEmpty(txtOtpCode.Text) Then
                litOtpValidResult.Text = "<div class='error'>Please enter both User ID and OTP Code</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                litOtpValidResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            Dim response = emailServiceClient.ValidateOTP(txtUserId.Text, txtOtpCode.Text, "LOGIN")

            If response.Success Then
                litOtpValidResult.Text = "<div class='success'>✅ OTP validation successful: " & response.Message & "</div>"
                AddDebugMessage("OTP validation successful for user: " & txtUserId.Text)
            Else
                litOtpValidResult.Text = "<div class='error'>❌ OTP validation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP validation failed: " & response.Message)
            End If

        Catch ex As Exception
            litOtpValidResult.Text = "<div class='error'>❌ OTP validation error: " & ex.Message & "</div>"
            AddDebugMessage("OTP validation exception: " & ex.Message)
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnCheckConfig_Click(sender As Object, e As EventArgs) Handles btnCheckConfig.Click
        Try
            Dim result As New System.Text.StringBuilder()
            result.AppendLine("<h4>OTP Configuration Status:</h4>")
            result.AppendLine("<pre>")

            ' Check OTP enabled setting
            Dim otpEnabled As String = ConfigurationManager.AppSettings("OtpEnabled")
            result.AppendLine("OtpEnabled: " & If(String.IsNullOrEmpty(otpEnabled), "NOT SET (defaults to true)", otpEnabled))

            ' Check email service URL
            Dim emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            result.AppendLine("EmailServiceUrl: " & If(String.IsNullOrEmpty(emailServiceUrl), "NOT SET", emailServiceUrl))

            ' Check database connection
            Try
                Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                    connection.Open()
                    result.AppendLine("Database Connection: ✅ SUCCESS")
                    connection.Close()
                End Using
            Catch ex As Exception
                result.AppendLine("Database Connection: ❌ FAILED - " & ex.Message)
            End Try

            result.AppendLine("</pre>")
            litConfigResult.Text = result.ToString()

        Catch ex As Exception
            litConfigResult.Text = "<div class='error'>Configuration check error: " & ex.Message & "</div>"
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnCheckUser_Click(sender As Object, e As EventArgs) Handles btnCheckUser.Click
        Try
            If String.IsNullOrEmpty(txtCheckUserId.Text) Then
                litUserCheckResult.Text = "<div class='error'>Please enter a User ID to check</div>"
                Return
            End If

            Dim result As New System.Text.StringBuilder()
            result.AppendLine("<h4>User Details for ID: " & Server.HtmlEncode(txtCheckUserId.Text) & "</h4>")

            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()

                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT nama, email, pwd, salt, password_migrated FROM pn_pengguna WHERE id_pg = ?"
                    command.Parameters.AddWithValue("", txtCheckUserId.Text)

                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            result.AppendLine("<pre>")
                            result.AppendLine("Name: " & If(reader("nama") Is DBNull.Value, "NULL", reader("nama").ToString()))
                            result.AppendLine("Email: " & If(reader("email") Is DBNull.Value, "NULL", reader("email").ToString()))
                            result.AppendLine("Password Length: " & If(reader("pwd") Is DBNull.Value, 0, reader("pwd").ToString().Length))
                            result.AppendLine("Salt Length: " & If(reader("salt") Is DBNull.Value, 0, reader("salt").ToString().Length))
                            result.AppendLine("Password Migrated: " & If(reader("password_migrated") Is DBNull.Value, "NULL", reader("password_migrated").ToString()))

                            ' Determine if OTP would be required
                            Dim email As String = If(reader("email") Is DBNull.Value, "", reader("email").ToString())
                            Dim hasSalt As Boolean = Not (reader("salt") Is DBNull.Value OrElse String.IsNullOrEmpty(reader("salt").ToString()))
                            Dim isMigrated As Boolean = If(reader("password_migrated") Is DBNull.Value, False, Convert.ToBoolean(reader("password_migrated")))

                            result.AppendLine("")
                            result.AppendLine("OTP Analysis:")
                            result.AppendLine("- Has valid email: " & (Not String.IsNullOrEmpty(email) AndAlso email.Contains("@")).ToString())
                            result.AppendLine("- Has encrypted password: " & (hasSalt OrElse isMigrated).ToString())
                            result.AppendLine("- Would require OTP: " & (Not String.IsNullOrEmpty(email) AndAlso email.Contains("@") AndAlso (hasSalt OrElse isMigrated)).ToString())
                            result.AppendLine("</pre>")
                        Else
                            result.AppendLine("<div class='error'>User not found</div>")
                        End If
                    End Using
                End Using
            End Using

            litUserCheckResult.Text = result.ToString()

        Catch ex As Exception
            litUserCheckResult.Text = "<div class='error'>User check error: " & ex.Message & "</div>"
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnTestLogin_Click(sender As Object, e As EventArgs) Handles btnTestLogin.Click
        Try
            If String.IsNullOrEmpty(txtTestUserId.Text) OrElse String.IsNullOrEmpty(txtTestPassword.Text) Then
                litLoginTestResult.Text = "<div class='error'>Please enter both User ID and Password</div>"
                Return
            End If

            Dim result As New System.Text.StringBuilder()
            result.AppendLine("<h4>Login Flow Test Results:</h4>")
            result.AppendLine("<pre>")

            ' Simulate login verification
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()

                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT pwd, salt, password_migrated, email FROM pn_pengguna WHERE id_pg = ?"
                    command.Parameters.AddWithValue("", txtTestUserId.Text)

                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            Dim storedPassword As String = If(reader("pwd") Is DBNull.Value, "", reader("pwd").ToString())
                            Dim salt As String = If(reader("salt") Is DBNull.Value, "", reader("salt").ToString())
                            Dim isMigrated As Boolean = If(reader("password_migrated") Is DBNull.Value, False, Convert.ToBoolean(reader("password_migrated")))
                            Dim email As String = If(reader("email") Is DBNull.Value, "", reader("email").ToString())

                            result.AppendLine("1. User found: ✅")

                            ' Test password verification
                            Dim passwordValid As Boolean = False
                            If Not String.IsNullOrEmpty(salt) AndAlso isMigrated Then
                                ' Encrypted password
                                Try
                                    passwordValid = PasswordHelper.VerifyPassword(txtTestPassword.Text, storedPassword, salt)
                                    result.AppendLine("2. Password verification (encrypted): " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                                Catch ex As Exception
                                    result.AppendLine("2. Password verification (encrypted): ❌ ERROR - " & ex.Message)
                                End Try
                            Else
                                ' Plain text password
                                passwordValid = storedPassword.Equals(txtTestPassword.Text)
                                result.AppendLine("2. Password verification (plain): " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                            End If

                            If passwordValid Then
                                ' Check OTP requirement
                                Dim otpRequired As Boolean = Not String.IsNullOrEmpty(email) AndAlso email.Contains("@") AndAlso (Not String.IsNullOrEmpty(salt) OrElse isMigrated)
                                result.AppendLine("3. OTP required: " & If(otpRequired, "✅ YES", "❌ NO"))

                                If otpRequired Then
                                    result.AppendLine("4. Next step: Redirect to OTP verification")
                                Else
                                    result.AppendLine("4. Next step: Direct login or password migration")
                                End If
                            Else
                                result.AppendLine("3. Login would fail due to invalid password")
                            End If

                        Else
                            result.AppendLine("1. User found: ❌ NOT FOUND")
                        End If
                    End Using
                End Using
            End Using

            result.AppendLine("</pre>")
            litLoginTestResult.Text = result.ToString()

        Catch ex As Exception
            litLoginTestResult.Text = "<div class='error'>Login test error: " & ex.Message & "</div>"
        End Try

        UpdateSummary()
    End Sub

    Protected Sub btnGetEmail_Click(sender As Object, e As EventArgs) Handles btnGetEmail.Click
        Try
            If String.IsNullOrEmpty(txtUserId.Text.Trim()) Then
                lblEmailResult.Text = "<div class='error'>Please enter a user ID</div>"
                Return
            End If

            Dim email As String = GetUserEmail(txtUserId.Text.Trim())
            If Not String.IsNullOrEmpty(email) Then
                lblEmailResult.Text = "<div class='success'>✅ Email found: " & email & "</div>"
                AddDebugMessage("Email lookup successful for user " & txtUserId.Text & ": " & email)
                
                ' Auto-populate OTP fields
                txtOtpUserId.Text = txtUserId.Text
                txtOtpEmail.Text = email
                txtValidateUserId.Text = txtUserId.Text
            Else
                lblEmailResult.Text = "<div class='error'>❌ No email found for user: " & txtUserId.Text & "</div>"
                AddDebugMessage("Email lookup failed for user " & txtUserId.Text)
            End If

        Catch ex As Exception
            lblEmailResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("Email lookup exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnGenerateOtp_Click(sender As Object, e As EventArgs) Handles btnGenerateOtp.Click
        Try
            If String.IsNullOrEmpty(txtOtpUserId.Text.Trim()) OrElse String.IsNullOrEmpty(txtOtpEmail.Text.Trim()) Then
                lblOtpResult.Text = "<div class='error'>Please enter both User ID and Email</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                lblOtpResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            AddDebugMessage("Attempting to generate OTP for user: " & txtOtpUserId.Text & ", email: " & txtOtpEmail.Text)
            
            Dim response = emailServiceClient.GenerateOTP(txtOtpUserId.Text.Trim(), txtOtpEmail.Text.Trim(), "LOGIN")
            
            If response.Success Then
                lblOtpResult.Text = "<div class='success'>✅ OTP generated successfully: " & response.Message & "</div>"
                AddDebugMessage("OTP generation successful: " & response.Message)
            Else
                lblOtpResult.Text = "<div class='error'>❌ OTP generation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP generation failed: " & response.Message)
            End If

        Catch ex As Exception
            lblOtpResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("OTP generation exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnValidateOtp_Click(sender As Object, e As EventArgs) Handles btnValidateOtp.Click
        Try
            If String.IsNullOrEmpty(txtValidateUserId.Text.Trim()) OrElse String.IsNullOrEmpty(txtOtpCode.Text.Trim()) Then
                lblValidateResult.Text = "<div class='error'>Please enter both User ID and OTP Code</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                lblValidateResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            AddDebugMessage("Attempting to validate OTP for user: " & txtValidateUserId.Text & ", code: " & txtOtpCode.Text)
            
            Dim response = emailServiceClient.ValidateOTP(txtValidateUserId.Text.Trim(), txtOtpCode.Text.Trim(), "LOGIN")
            
            If response.Success Then
                lblValidateResult.Text = "<div class='success'>✅ OTP validation successful: " & response.Message & "</div>"
                AddDebugMessage("OTP validation successful: " & response.Message)
            Else
                lblValidateResult.Text = "<div class='error'>❌ OTP validation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP validation failed: " & response.Message)
            End If

        Catch ex As Exception
            lblValidateResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("OTP validation exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnCheckConfig_Click(sender As Object, e As EventArgs) Handles btnCheckConfig.Click
        Try
            Dim configInfo As New System.Text.StringBuilder()
            
            ' Check configuration settings
            Dim emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            Dim otpEnabled As String = ConfigurationManager.AppSettings("OtpEnabled")
            Dim emailServiceEnabled As String = ConfigurationManager.AppSettings("EmailServiceEnabled")
            
            configInfo.AppendLine("EmailServiceUrl: " & If(emailServiceUrl, "NOT SET"))
            configInfo.AppendLine("OtpEnabled: " & If(otpEnabled, "NOT SET"))
            configInfo.AppendLine("EmailServiceEnabled: " & If(emailServiceEnabled, "NOT SET"))
            
            ' Check database connection
            Try
                Dim cn As New OleDbConnection(SPMJ_Mod.ServerId)
                cn.Open()
                configInfo.AppendLine("Database Connection: ✅ SUCCESS")
                cn.Close()
            Catch ex As Exception
                configInfo.AppendLine("Database Connection: ❌ FAILED - " & ex.Message)
            End Try
            
            lblConfigResult.Text = "<div class='info'><pre>" & configInfo.ToString() & "</pre></div>"
            AddDebugMessage("Configuration check completed")

        Catch ex As Exception
            lblConfigResult.Text = "<div class='error'>❌ Configuration check error: " & ex.Message & "</div>"
            AddDebugMessage("Configuration check exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnClearLog_Click(sender As Object, e As EventArgs) Handles btnClearLog.Click
        debugMessages.Clear()
        UpdateDebugLog()
    End Sub

    Private Sub AddDebugMessage(message As String)
        Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
        debugMessages.Add("[" & timestamp & "] " & message)
    End Sub

    Private Sub UpdateDebugLog()
        debugLog.InnerHtml = String.Join(vbCrLf, debugMessages.ToArray())
    End Sub

    Private Sub UpdateSummary()
        Try
            Dim summary As New System.Text.StringBuilder()
            summary.AppendLine("<h4>🔍 Test Summary</h4>")
            summary.AppendLine("<div class='info'>")
            summary.AppendLine("Total debug messages: " & debugMessages.Count & "<br/>")
            summary.AppendLine("Last test run: " & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") & "<br/>")
            summary.AppendLine("</div>")

            If debugMessages.Count > 0 Then
                summary.AppendLine("<h5>Recent Messages:</h5>")
                summary.AppendLine("<pre>")
                Dim recentMessages = debugMessages.TakeLast(5)
                For Each msg In recentMessages
                    summary.AppendLine(Server.HtmlEncode(msg))
                Next
                summary.AppendLine("</pre>")
            End If

            litSummary.Text = summary.ToString()
        Catch ex As Exception
            litSummary.Text = "<div class='error'>Summary update error: " & ex.Message & "</div>"
        End Try
    End Sub

    Private Function GetUserEmail(userId As String) As String
        Try
            Dim email As String = ""
            Dim cn As New OleDbConnection
            Dim cmd As New OleDbCommand
            
            cn.ConnectionString = SPMJ_Mod.ServerId
            cn.Open()
            cmd.Connection = cn
            cmd.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ? AND status = 1"
            cmd.Parameters.AddWithValue("", userId)
            
            Dim result = cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                email = result.ToString().Trim()
            End If
            
            cn.Close()
            Return email
        Catch ex As Exception
            AddDebugMessage("GetUserEmail exception: " & ex.Message)
            Return ""
        End Try
    End Function

End Class
