<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="TestPasswordRecoveryToggle.aspx.vb" Inherits="SPMJ.TestPasswordRecoveryToggle" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Test Password Recovery Toggle</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Test Password Recovery Toggle Functionality</h1>
        
        <div class="test-section">
            <h3>Test "Lupa Kata Laluan?" Link Toggle</h3>
            <p>This simulates the login page functionality:</p>
            
            <asp:LinkButton ID="lnk_TestForgotPassword" runat="server" 
                Font-Names="Arial" Font-Size="8pt" ForeColor="Blue" 
                Text="Lupa Kata Laluan?" OnClick="lnk_TestForgotPassword_Click" />
            
            <br /><br />
            
            <asp:Panel ID="pnl_TestPasswordRecovery" runat="server" Visible="false" 
                style="border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;">
                <h4>Password Recovery Panel</h4>
                <p>ID Pengguna:</p>
                <asp:TextBox ID="txt_TestRecoveryUserId" runat="server" Width="200px"></asp:TextBox>
                <br /><br />
                <asp:Button ID="btn_TestSendRecovery" runat="server" Text="Hantar" />
                <asp:Button ID="btn_TestCancelRecovery" runat="server" Text="Batal" OnClick="btn_TestCancelRecovery_Click" />
                <br /><br />
                <asp:Label ID="lbl_TestRecoveryMessage" runat="server" Text=""></asp:Label>
            </asp:Panel>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <asp:Label ID="lbl_TestResults" runat="server" Text="Click the 'Lupa Kata Laluan?' link to test toggle functionality."></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>Expected Behavior</h3>
            <ul>
                <li><strong>First click:</strong> Should show the password recovery panel</li>
                <li><strong>Second click:</strong> Should hide the password recovery panel</li>
                <li><strong>Third click:</strong> Should show the panel again</li>
                <li><strong>Cancel button:</strong> Should hide the panel</li>
                <li><strong>No "Tunjuk Email" button:</strong> Should be removed completely</li>
                <li><strong>Full email addresses:</strong> Should be shown directly without masking</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Navigation</h3>
            <asp:Button ID="btn_GoToLogin" runat="server" Text="Go to Actual Login Page" OnClick="btn_GoToLogin_Click" />
        </div>
    </form>
</body>
</html>
