﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Kolej.Master" CodeBehind="Login_J.aspx.vb" Inherits="SPMJ.Login_J" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">


    <style type="text/css">
        .style1
        {
            width: 627px;
        }
        .style2
        {
        }
        .label
{
	font-family: Arial;
	font-size: 8pt;
	font-variant: small-caps;
}
        .style6
    {
        width: 58px;
    }
        .style7
        {
        }
        .style8
        {
            width: 8px;
        }
        .style9
        {
            font-weight: bold;
            text-decoration: underline;
            color: #CC0000;
        }
        .style10
        {
            width: 58px;
            height: 22px;
        }
        .style11
        {
            height: 22px;
        }
        .style12
        {
            width: 58px;
            height: 18px;
        }
        .style13
        {
            height: 18px;
        }
        .style14
        {
            width: 58px;
            height: 24px;
        }
        .style15
        {
            height: 24px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    
   <%-- <link href="https://fonts.googleapis.com/css?family=Lato" rel='stylesheet' type='text/css'>--%>
       <%-- <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">--%>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js" type="text/javascript"></script>
    <script src="jquery.pause.js" type="text/javascript"></script>
    <script src="jquery.easing.min.js" type="text/javascript"></script>
    <!--<script src="//rawgithub.com/aamirafridi/jQuery.Marquee/master/jquery.marquee.min.js?v=3" type="text/javascript"></script>-->
    <script src="jquery.marquee.js?v=3" type="text/javascript"></script>
    <style type="text/css">
      body {
        margin: 10px;
        font-family: 'Lato', sans-serif;
      }
      small {
        font-size: 14px;
      }
      h1 {
        margin-bottom: 20px;
        padding-bottom: 10px;
        text-align: center;
      }

      h2 {
        border-bottom: 1px dotted #ccc;
        padding-bottom: 5px;
        margin-bottom: 10px;
      }

      .marquee,
      .marquee-with-options,
      .marquee-vert-before,
      .marquee-vert {
        width: 566px;
        overflow: hidden;
        border:1px solid #ccc;
            height: 157px;
        }
    </style>
    <table style="width:100%; height: 315px;" cellpadding="-1" cellspacing="-1">
        <tr>
            <td class="style8" valign="top" 
                style="background-image: url('../../Image/Bg_Dot2.gif')">&nbsp;</td>
               <td class="style1" valign="middle" 
                style="background-image: url('../../Image/Bg_Dot2.gif')" height="100%">
                   <%--<div class='marquee-vert' style='height:700px' data-gap=0 data-speed=3000 data-direction='up'><asp:Label ID="Label1" runat="server" Text="Label" Width="100%"  height="220px" BorderStyle="None" BorderWidth="0" Font-Names="Arial" Font-Size="8pt"></asp:Label></div>--%>
                   <li><asp:Label ID="Label1" runat="server" Text="Label" Width="100%"  height="220px" BorderStyle="None" BorderWidth="0" Font-Names="Arial" Font-Size="8pt"></asp:Label></li>                     
                   </td>
           <%-- <td class="style1" valign="middle" 
                style="background-image: url('../../Image/Bg_Dot2.gif')" height="100%"><marquee onmouseover="stop();" onmouseout="start();" scrollAmount="1.5" scrollDelay="1" direction="up" width="100%" height="100%"><asp:Label ID="Label1" runat="server" Text="Label" Width="100%"  height="220px" BorderStyle="None" BorderWidth="0" Font-Names="Arial" Font-Size="8pt"></asp:Label></marquee></td>--%>
            <td align="left" valign="top" style="border-color: #000000; border-left-style: solid; border-width: 1px; background-image: url('../../Image/LJM-Bg.gif'); background-attachment: fixed; background-repeat: no-repeat;">
                <table style="background-position: 250px 90px; width: 100%; font-family: Arial; font-size: 8pt; font-variant: small-caps; background-image: url('../../Image/LJM-Bg.gif'); background-repeat: no-repeat; height: 100%;">
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style2" colspan="2">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style14">
                            </td>                        <td class="style15" colspan="2">
                            <asp:Label ID="LbL" runat="server" ForeColor="#CC0000"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td class="style10">
                            </td>
                        <td class="style11">
                            Kod Pengguna</td>                        <td class="style11">
                            <asp:TextBox ID="Tx_Id" runat="server" 
    CssClass="label" MaxLength="15"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td class="style12">
                            </td>
                        <td class="style13">
                            Kata Laluan</td>                        <td class="style13">
                            <asp:TextBox ID="Tx_Pwd" runat="server" 
    CssClass="label" MaxLength="15" TextMode="Password"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td class="style10">
                            </td>
                        <td class="style11">
                            </td>                        <td class="style11">
                            <asp:Button ID="Button1" runat="server"
    Text="LOGIN" CssClass="label"
                                Width="50px" />
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7">
                            &nbsp;</td>
                        <td>
                            <asp:LinkButton ID="lnk_ForgotPassword" runat="server"
                                Font-Names="Arial" Font-Size="8pt" ForeColor="Blue"
                                Text="Lupa Kata Laluan?" OnClick="lnk_ForgotPassword_Click" />
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7" colspan="2">
                            <!-- Password Recovery Panel -->
                            <asp:Panel ID="pnl_PasswordRecovery" runat="server" Visible="false"
                                style="border: 1px solid #ccc; padding: 10px; margin-top: 10px; background-color: #f9f9f9;">
                                <table style="width: 100%; font-family: Arial; font-size: 8pt;">
                                    <tr>
                                        <td colspan="2" style="font-weight: bold; color: #CC0000; margin-bottom: 10px;">
                                            Reset Kata Laluan
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 100px;">ID Pengguna:</td>
                                        <td>
                                            <asp:TextBox ID="txt_RecoveryUserId" runat="server"
                                                CssClass="label" MaxLength="15" Width="150px"></asp:TextBox>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>
                                            <asp:Button ID="btn_SendRecovery" runat="server"
                                                Text="Hantar" CssClass="label" Width="60px"
                                                OnClick="btn_SendRecovery_Click" />
                                            <asp:Button ID="btn_CancelRecovery" runat="server"
                                                Text="Batal" CssClass="label" Width="60px"
                                                OnClick="btn_CancelRecovery_Click" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2">
                                            <br />
                                            <asp:Label ID="lbl_RecoveryMessage" runat="server"
                                                Font-Names="Arial" Font-Size="8pt" />
                                        </td>
                                    </tr>
                                </table>
                            </asp:Panel>
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                        <td class="style7" colspan="2" rowspan="2">
                            <span class="style9">
                            <br />
                            Perhatian<br />
                            </span><br />
                            Sistem ini dibangunkan khas utuk kegunaan Kolej-Kolej Kejururawatan sahaja. 
                            Untuk sebarang pertanyaan mengenai penggunaan sistem ini, sila hubungi:<br />
                            <br />
                            Bahagian Kejururawatan, kkm<br />
                            <br />
                            <br />
                            <br />
                        </td>
                    </tr>
                    <tr>
                        <td class="style6">
                            &nbsp;</td>
                    </tr>
                    </table>
            </td></tr>
    </table>
</asp:Content>
