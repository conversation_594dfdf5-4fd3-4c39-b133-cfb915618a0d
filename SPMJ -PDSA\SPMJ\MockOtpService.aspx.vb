Imports System.Text

Partial Public Class MockOtpService
    Inherits System.Web.UI.Page

    ' Mock OTP storage (in production this would be in database)
    Private Shared otpStorage As New Dictionary(Of String, MockOtpData)()
    Private Shared logMessages As New List(Of String)()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            AddLog("Mock OTP Service initialized")
            UpdateLog()
        End If
    End Sub

    Protected Sub btnTestHealth_Click(sender As Object, e As EventArgs) Handles btnTestHealth.Click
        Try
            litHealthResult.Text = "<div class='success'>✅ Mock OTP Service is healthy</div>"
            AddLog("Health check requested - SUCCESS")
            UpdateLog()
        Catch ex As Exception
            litHealthResult.Text = "<div class='error'>❌ Health check failed: " & ex.Message & "</div>"
            AddLog("Health check failed: " & ex.Message)
            UpdateLog()
        End Try
    End Sub

    Protected Sub btnGenerateOtp_Click(sender As Object, e As EventArgs) Handles btnGenerateOtp.Click
        Try
            If String.IsNullOrEmpty(txtUserId.Text) OrElse String.IsNullOrEmpty(txtEmail.Text) Then
                litGenerateResult.Text = "<div class='error'>❌ User ID and Email are required</div>"
                Return
            End If

            ' Generate mock OTP
            Dim otpCode As String = GenerateMockOtp()
            Dim key As String = txtUserId.Text & "_" & txtPurpose.Text.ToUpper()
            
            ' Store OTP (simulate database storage)
            otpStorage(key) = New MockOtpData With {
                .UserId = txtUserId.Text,
                .Email = txtEmail.Text,
                .Purpose = txtPurpose.Text.ToUpper(),
                .OtpCode = otpCode,
                .CreatedAt = DateTime.Now,
                .ExpiresAt = DateTime.Now.AddMinutes(5),
                .Used = False
            }

            Dim result As New StringBuilder()
            result.AppendLine("<div class='success'>✅ OTP Generated Successfully</div>")
            result.AppendLine("<pre>")
            result.AppendLine("User ID: " & txtUserId.Text)
            result.AppendLine("Email: " & txtEmail.Text)
            result.AppendLine("Purpose: " & txtPurpose.Text.ToUpper())
            result.AppendLine("OTP Code: " & otpCode)
            result.AppendLine("Expires: " & DateTime.Now.AddMinutes(5).ToString("yyyy-MM-dd HH:mm:ss"))
            result.AppendLine("</pre>")
            
            litGenerateResult.Text = result.ToString()
            AddLog("OTP generated for " & txtUserId.Text & " - Code: " & otpCode)
            UpdateLog()

        Catch ex As Exception
            litGenerateResult.Text = "<div class='error'>❌ OTP generation failed: " & ex.Message & "</div>"
            AddLog("OTP generation failed: " & ex.Message)
            UpdateLog()
        End Try
    End Sub

    Protected Sub btnValidateOtp_Click(sender As Object, e As EventArgs) Handles btnValidateOtp.Click
        Try
            If String.IsNullOrEmpty(txtValidateUserId.Text) OrElse String.IsNullOrEmpty(txtOtpCode.Text) Then
                litValidateResult.Text = "<div class='error'>❌ User ID and OTP Code are required</div>"
                Return
            End If

            Dim key As String = txtValidateUserId.Text & "_" & txtValidatePurpose.Text.ToUpper()
            
            If otpStorage.ContainsKey(key) Then
                Dim otpData As MockOtpData = otpStorage(key)
                
                Dim result As New StringBuilder()
                result.AppendLine("<pre>")
                result.AppendLine("Stored OTP: " & otpData.OtpCode)
                result.AppendLine("Provided OTP: " & txtOtpCode.Text)
                result.AppendLine("Expires At: " & otpData.ExpiresAt.ToString("yyyy-MM-dd HH:mm:ss"))
                result.AppendLine("Current Time: " & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                result.AppendLine("Already Used: " & otpData.Used.ToString())
                result.AppendLine("")

                If otpData.Used Then
                    result.AppendLine("Result: ❌ OTP already used")
                    litValidateResult.Text = "<div class='error'>" & result.ToString() & "</div>"
                ElseIf DateTime.Now > otpData.ExpiresAt Then
                    result.AppendLine("Result: ❌ OTP expired")
                    litValidateResult.Text = "<div class='error'>" & result.ToString() & "</div>"
                ElseIf otpData.OtpCode = txtOtpCode.Text Then
                    otpData.Used = True
                    result.AppendLine("Result: ✅ OTP validation successful")
                    litValidateResult.Text = "<div class='success'>" & result.ToString() & "</div>"
                    AddLog("OTP validated successfully for " & txtValidateUserId.Text)
                Else
                    result.AppendLine("Result: ❌ Invalid OTP code")
                    litValidateResult.Text = "<div class='error'>" & result.ToString() & "</div>"
                End If
                result.AppendLine("</pre>")
            Else
                litValidateResult.Text = "<div class='error'>❌ No OTP found for this user and purpose</div>"
                AddLog("OTP validation failed - no OTP found for " & txtValidateUserId.Text)
            End If
            
            UpdateLog()

        Catch ex As Exception
            litValidateResult.Text = "<div class='error'>❌ OTP validation failed: " & ex.Message & "</div>"
            AddLog("OTP validation error: " & ex.Message)
            UpdateLog()
        End Try
    End Sub

    Protected Sub btnTestClient_Click(sender As Object, e As EventArgs) Handles btnTestClient.Click
        Try
            ' Test the EmailServiceClient against this mock service
            ' Note: This would require the EmailServiceClient to point to this mock service
            Dim result As New StringBuilder()
            result.AppendLine("<div class='info'>")
            result.AppendLine("<h4>EmailServiceClient Test Instructions:</h4>")
            result.AppendLine("<p>To test the EmailServiceClient integration:</p>")
            result.AppendLine("<ol>")
            result.AppendLine("<li>Update Web.config EmailServiceUrl to point to this mock service</li>")
            result.AppendLine("<li>Use the login page to test OTP flow</li>")
            result.AppendLine("<li>Check this log for OTP generation/validation requests</li>")
            result.AppendLine("</ol>")
            result.AppendLine("<p><strong>Current Mock Service Status:</strong></p>")
            result.AppendLine("<pre>")
            result.AppendLine("Active OTPs: " & otpStorage.Count)
            result.AppendLine("Service Uptime: " & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            result.AppendLine("</pre>")
            result.AppendLine("</div>")
            
            litClientResult.Text = result.ToString()
            AddLog("EmailServiceClient test information displayed")
            UpdateLog()

        Catch ex As Exception
            litClientResult.Text = "<div class='error'>❌ Client test error: " & ex.Message & "</div>"
            AddLog("Client test error: " & ex.Message)
            UpdateLog()
        End Try
    End Sub

    Private Function GenerateMockOtp() As String
        ' Generate a 6-digit OTP
        Dim random As New Random()
        Return random.Next(100000, 999999).ToString()
    End Function

    Private Sub AddLog(message As String)
        Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
        logMessages.Add("[" & timestamp & "] " & message)
        
        ' Keep only last 20 messages
        If logMessages.Count > 20 Then
            logMessages.RemoveAt(0)
        End If
    End Sub

    Private Sub UpdateLog()
        Dim logHtml As New StringBuilder()
        logHtml.AppendLine("<h4>Service Activity Log:</h4>")
        logHtml.AppendLine("<pre>")
        For Each msg In logMessages
            logHtml.AppendLine(Server.HtmlEncode(msg))
        Next
        logHtml.AppendLine("</pre>")
        litLog.Text = logHtml.ToString()
    End Sub

    ' Mock OTP data structure
    Public Class MockOtpData
        Public Property UserId As String
        Public Property Email As String
        Public Property Purpose As String
        Public Property OtpCode As String
        Public Property CreatedAt As DateTime
        Public Property ExpiresAt As DateTime
        Public Property Used As Boolean
    End Class

End Class
