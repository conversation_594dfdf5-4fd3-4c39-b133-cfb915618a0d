<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="TestLoginFlow.aspx.vb" Inherits="SPMJ.TestLoginFlow" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Login Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #eee; padding: 10px; overflow-x: auto; }
        .button { padding: 10px 15px; margin: 5px; }
        input[type="text"], input[type="password"] { width: 200px; padding: 5px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>🔐 Login Flow Test Suite</h1>
        
        <div class="test-section">
            <h3>1. Database Connection Test</h3>
            <asp:Button ID="btnTestDatabase" runat="server" Text="Test Database Connection" CssClass="button" />
            <div id="databaseResult">
                <asp:Literal ID="litDatabaseResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. User Lookup Test</h3>
            <p>User ID: <asp:TextBox ID="txtUserId" runat="server" Text=""></asp:TextBox></p>
            <asp:Button ID="btnLookupUser" runat="server" Text="Lookup User" CssClass="button" />
            <div id="userResult">
                <asp:Literal ID="litUserResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. Password Verification Test</h3>
            <p>User ID: <asp:TextBox ID="txtTestUserId" runat="server" Text=""></asp:TextBox></p>
            <p>Password: <asp:TextBox ID="txtTestPassword" runat="server" TextMode="Password"></asp:TextBox></p>
            <asp:Button ID="btnTestPassword" runat="server" Text="Test Password" CssClass="button" />
            <div id="passwordResult">
                <asp:Literal ID="litPasswordResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. OTP Requirement Check</h3>
            <p>User ID: <asp:TextBox ID="txtOtpUserId" runat="server" Text=""></asp:TextBox></p>
            <asp:Button ID="btnCheckOtpReq" runat="server" Text="Check OTP Requirement" CssClass="button" />
            <div id="otpReqResult">
                <asp:Literal ID="litOtpReqResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. Create Test User</h3>
            <p>User ID: <asp:TextBox ID="txtNewUserId" runat="server" Text="testuser001"></asp:TextBox></p>
            <p>Name: <asp:TextBox ID="txtNewUserName" runat="server" Text="Test User"></asp:TextBox></p>
            <p>Email: <asp:TextBox ID="txtNewUserEmail" runat="server" Text="<EMAIL>"></asp:TextBox></p>
            <p>Password: <asp:TextBox ID="txtNewUserPassword" runat="server" Text="password123"></asp:TextBox></p>
            <p>
                <asp:CheckBox ID="chkEncrypted" runat="server" Text="Create with encrypted password" Checked="true" />
            </p>
            <asp:Button ID="btnCreateUser" runat="server" Text="Create Test User" CssClass="button" />
            <div id="createUserResult">
                <asp:Literal ID="litCreateUserResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>6. Simulate Login Process</h3>
            <p>User ID: <asp:TextBox ID="txtLoginUserId" runat="server" Text=""></asp:TextBox></p>
            <p>Password: <asp:TextBox ID="txtLoginPassword" runat="server" TextMode="Password"></asp:TextBox></p>
            <asp:Button ID="btnSimulateLogin" runat="server" Text="Simulate Login" CssClass="button" />
            <div id="loginResult">
                <asp:Literal ID="litLoginResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Results Summary</h3>
            <asp:Literal ID="litSummary" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
