<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="QuickTestKolejLogin.aspx.vb" Inherits="SPMJ.QuickTestKolejLogin" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Quick Test KOLEJ Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-good { color: green; font-weight: bold; }
        .status-bad { color: red; font-weight: bold; }
        .status-warning { color: orange; font-weight: bold; }
        .test-section { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SPMJ KOLEJ-PDSA Quick Test</h1>
        
        <form id="form1" runat="server">
            <div class="test-section">
                <h3>🔍 System Status Check</h3>
                <asp:Button ID="btnQuickCheck" runat="server" Text="Run Quick System Check" 
                    CssClass="btn btn-primary" OnClick="btnQuickCheck_Click" />
                <br /><br />
                <asp:Label ID="lblQuickCheckResult" runat="server"></asp:Label>
            </div>
            
            <div class="test-section">
                <h3>👤 Test User Creation</h3>
                <p>Create a test user with encrypted password and email for testing:</p>
                User ID: <asp:TextBox ID="txtQuickUserId" runat="server" Text="kolejtest" />
                Email: <asp:TextBox ID="txtQuickEmail" runat="server" Text="<EMAIL>" />
                <br /><br />
                <asp:Button ID="btnCreateTestUser" runat="server" Text="Create Test User" 
                    CssClass="btn btn-success" OnClick="btnCreateTestUser_Click" />
                <br /><br />
                <asp:Label ID="lblTestUserResult" runat="server"></asp:Label>
            </div>
            
            <div class="test-section">
                <h3>🔐 Test OTP Flow</h3>
                <p>Test the complete OTP generation and verification flow:</p>
                <asp:Button ID="btnTestOtpFlow" runat="server" Text="Test OTP Generation" 
                    CssClass="btn btn-warning" OnClick="btnTestOtpFlow_Click" />
                <br /><br />
                <asp:Label ID="lblOtpFlowResult" runat="server"></asp:Label>
            </div>
            
            <div class="test-section">
                <h3>🔗 Navigation Links</h3>
                <asp:Button ID="btnGoToKolejLogin" runat="server" Text="Go to KOLEJ Login Page" 
                    CssClass="btn btn-primary" OnClick="btnGoToKolejLogin_Click" />
                <asp:Button ID="btnGoToFullTest" runat="server" Text="Go to Full Test Page" 
                    CssClass="btn btn-primary" OnClick="btnGoToFullTest_Click" />
            </div>
            
            <div class="test-section">
                <h3>📋 Test Credentials</h3>
                <p><strong>Test User Credentials:</strong></p>
                <ul>
                    <li><strong>User ID:</strong> <span id="spanUserId" runat="server">kolejtest</span></li>
                    <li><strong>Password:</strong> kolej123</li>
                    <li><strong>Email:</strong> <span id="spanEmail" runat="server"><EMAIL></span></li>
                    <li><strong>Password Type:</strong> Encrypted (triggers OTP)</li>
                </ul>
                
                <p><strong>Expected Flow:</strong></p>
                <ol>
                    <li>Login with above credentials on Login_J.aspx</li>
                    <li>System validates encrypted password</li>
                    <li>OTP sent to email address</li>
                    <li>Redirect to OtpVerification.aspx</li>
                    <li>Enter OTP code to complete login</li>
                    <li>Redirect to blank.aspx on success</li>
                </ol>
            </div>
        </form>
    </div>
</body>
</html>
