<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="TestCompleteOtpFlow.aspx.vb" Inherits="SPMJ.TestCompleteOtpFlow" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Test Complete OTP Flow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Complete OTP Flow Test</h1>
        
        <div class="test-section">
            <h3>1. Test OTP Microservice Health</h3>
            <asp:Button ID="btnTestHealth" runat="server" Text="Test Health" OnClick="btnTestHealth_Click" />
            <br /><br />
            <asp:Label ID="lblHealthResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>2. Test OTP Generation</h3>
            User ID: <asp:TextBox ID="txtTestUserId" runat="server" Text="testuser"></asp:TextBox>
            Email: <asp:TextBox ID="txtTestEmail" runat="server" Text="<EMAIL>"></asp:TextBox>
            <asp:Button ID="btnTestGenerate" runat="server" Text="Generate OTP" OnClick="btnTestGenerate_Click" />
            <br /><br />
            <asp:Label ID="lblGenerateResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>3. Test OTP Validation</h3>
            User ID: <asp:TextBox ID="txtValidateUserId" runat="server" Text="testuser"></asp:TextBox>
            OTP Code: <asp:TextBox ID="txtOtpCode" runat="server" Text="123456" MaxLength="6"></asp:TextBox>
            <asp:Button ID="btnTestValidate" runat="server" Text="Validate OTP" OnClick="btnTestValidate_Click" />
            <br /><br />
            <asp:Label ID="lblValidateResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>4. Test OTP Verification Page Simulation</h3>
            <asp:Button ID="btnTestVerificationPage" runat="server" Text="Test Verification Page Logic" OnClick="btnTestVerificationPage_Click" />
            <br /><br />
            <asp:Label ID="lblVerificationResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>5. Test Session Setup (Simulate Login)</h3>
            <asp:Button ID="btnSetupSession" runat="server" Text="Setup Test Session" OnClick="btnSetupSession_Click" />
            <asp:Button ID="btnClearSession" runat="server" Text="Clear Session" OnClick="btnClearSession_Click" />
            <br /><br />
            <asp:Label ID="lblSessionResult" runat="server" Text=""></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>6. Navigation Test</h3>
            <asp:Button ID="btnGoToLogin" runat="server" Text="Go to Login Page" OnClick="btnGoToLogin_Click" />
            <asp:Button ID="btnGoToOtpVerification" runat="server" Text="Go to OTP Verification" OnClick="btnGoToOtpVerification_Click" />
            <br /><br />
            <asp:Label ID="lblNavigationResult" runat="server" Text=""></asp:Label>
        </div>
    </form>
</body>
</html>
