<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="DebugPasswordVerification.aspx.vb" Inherits="SPMJ.DebugPasswordVerification" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Debug Password Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #eee; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Password Verification Debug Tool</h1>
        
        <div class="debug-section">
            <h3>Test Password Verification</h3>
            <p>User ID: <asp:TextBox ID="txtUserId" runat="server" Text=""></asp:TextBox></p>
            <p>Test Password: <asp:TextBox ID="txtPassword" runat="server" TextMode="Password"></asp:TextBox></p>
            <asp:Button ID="btnTest" runat="server" Text="Test Verification" />
        </div>
        
        <div class="debug-section">
            <h3>Database Information</h3>
            <asp:Literal ID="litDatabaseInfo" runat="server"></asp:Literal>
        </div>
        
        <div class="debug-section">
            <h3>Debug Output</h3>
            <asp:Literal ID="litDebugOutput" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
