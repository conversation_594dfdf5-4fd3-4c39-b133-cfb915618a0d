<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="AddTestUser.aspx.vb" Inherits="SPMJ.AddTestUser" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Add Test User</title>
</head>
<body>
    <form id="form1" runat="server">
        <h2>Add Test User for OTP Testing</h2>
        
        <table>
            <tr>
                <td>User ID:</td>
                <td><asp:TextBox ID="txtUserId" runat="server" Text="testuser"></asp:TextBox></td>
            </tr>
            <tr>
                <td>Password:</td>
                <td><asp:TextBox ID="txtPassword" runat="server" Text="test123" TextMode="Password"></asp:TextBox></td>
            </tr>
            <tr>
                <td>Email:</td>
                <td><asp:TextBox ID="txtEmail" runat="server" Text="<EMAIL>"></asp:TextBox></td>
            </tr>
            <tr>
                <td>Name:</td>
                <td><asp:TextBox ID="txtName" runat="server" Text="Test User"></asp:TextBox></td>
            </tr>
            <tr>
                <td colspan="2">
                    <asp:Button ID="btnAddUser" runat="server" Text="Add Test User" OnClick="btnAddUser_Click" />
                    <asp:Button ID="btnUpdateExisting" runat="server" Text="Update Existing User Email" OnClick="btnUpdateExisting_Click" />
                </td>
            </tr>
        </table>
        
        <br /><br />
        <asp:Label ID="lblResult" runat="server" Text=""></asp:Label>
    </form>
</body>
</html>
