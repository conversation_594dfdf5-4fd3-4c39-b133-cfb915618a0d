# Test EmailServiceClient CheckHealth Method Simulation
Write-Host "Simulating EmailServiceClient.CheckHealth() method..." -ForegroundColor Cyan

$baseUrl = "http://localhost:5000"
$apiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
$timeout = 30000

try {
    # Simulate the GetRequest("/health") call
    $url = $baseUrl + "/health"
    Write-Host "URL: $url" -ForegroundColor Yellow
    
    # Create HttpWebRequest like in VB.NET
    $request = [System.Net.WebRequest]::Create($url)
    $request.Method = "GET"
    $request.Headers.Add("X-API-Key", $apiKey)
    $request.Timeout = $timeout
    
    Write-Host "Making request with API key..." -ForegroundColor Yellow
    
    # Get response
    $response = $request.GetResponse()
    $responseStream = $response.GetResponseStream()
    $reader = New-Object System.IO.StreamReader($responseStream)
    $responseText = $reader.ReadToEnd()
    
    Write-Host "Response Text: '$responseText'" -ForegroundColor White
    
    # Test the same logic as in CheckHealth()
    $isNotEmpty = -not [string]::IsNullOrEmpty($responseText)
    $containsHealthy = $responseText.Contains("healthy")
    $containsOk = $responseText.Contains("ok")
    $isHealthy = $isNotEmpty -and ($containsHealthy -or $containsOk)
    
    Write-Host "Is Not Empty: $isNotEmpty" -ForegroundColor Cyan
    Write-Host "Contains 'healthy': $containsHealthy" -ForegroundColor Cyan
    Write-Host "Contains 'ok': $containsOk" -ForegroundColor Cyan
    Write-Host "Final IsHealthy: $isHealthy" -ForegroundColor $(if($isHealthy) { 'Green' } else { 'Red' })
    
    # Clean up
    $reader.Close()
    $responseStream.Close()
    $response.Close()
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor Red
    if ($_.Exception.InnerException) {
        Write-Host "Inner Exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
    }
}
