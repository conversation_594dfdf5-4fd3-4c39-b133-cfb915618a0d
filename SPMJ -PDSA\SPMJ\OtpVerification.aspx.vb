Imports System.Data.OleDb

Partial Public Class OtpVerification
    Inherits System.Web.UI.Page

    Private emailServiceClient As EmailServiceClient

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Initialize email service client (always, not just on first load)
        Try
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"

            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Initializing email service client with URL: " & serviceUrl)
            emailServiceClient = New EmailServiceClient(serviceUrl)
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Email service client initialized successfully")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Failed to initialize email service client: " & ex.Message)
            emailServiceClient = Nothing
        End Try

        ' Check if user has valid temporary session
        If Session("TEMP_USER_ID") Is Nothing Then
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: No temporary user session found, redirecting to login")
            Response.Redirect("p0_Login.aspx")
            Return
        End If

        If Not IsPostBack Then
            ' Show welcome message instead of generating new OTP (OTP was already sent from login page)
            ShowWelcomeMessage()
        End If
    End Sub

    Private Sub ShowWelcomeMessage()
        Try
            Dim userId As String = Session("TEMP_USER_ID").ToString()
            Dim userEmail As String = ""

            ' Get user's email to show masked version
            If Session("USER_EMAIL") IsNot Nothing Then
                userEmail = Session("USER_EMAIL").ToString()
            Else
                userEmail = GetUserEmail(userId)
            End If

            If Not String.IsNullOrEmpty(userEmail) Then
                ShowMessage("Kod OTP telah dihantar ke email anda: " & MaskEmail(userEmail) & ". Sila masukkan kod untuk meneruskan.", "info")
            Else
                ShowMessage("Kod OTP telah dihantar. Sila masukkan kod untuk meneruskan.", "info")
            End If

            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Welcome message shown for user: " & userId)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Error showing welcome message: " & ex.Message)
            ShowMessage("Sila masukkan kod OTP yang telah dihantar untuk meneruskan.", "info")
        End Try
    End Sub

    Private Sub GenerateAndSendOtp()
        Try
            Dim userId As String = Session("TEMP_USER_ID").ToString()
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Generating OTP for user: " & userId)

            ' Get user's email
            Dim userEmail As String = ""
            If Session("USER_EMAIL") IsNot Nothing Then
                userEmail = Session("USER_EMAIL").ToString()
            Else
                userEmail = GetUserEmail(userId)
            End If

            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: No email found for user: " & userId)
                ShowMessage("Alamat email tidak dijumpai untuk akaun ini", "danger")
                Return
            End If

            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: User email: " & MaskEmail(userEmail))

            ' Check if email service client is available
            If emailServiceClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Email service client is Nothing, cannot generate OTP")
                ShowMessage("Sistem OTP tidak dapat dimulakan. Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
                Return
            End If

            ' Check if email service is available (with fallback)
            Dim isHealthy As Boolean = False
            Try
                isHealthy = emailServiceClient.CheckHealth()
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Primary health check result: " & isHealthy.ToString())

                If Not isHealthy Then
                    System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Primary health check failed, trying simple health check...")
                    isHealthy = emailServiceClient.CheckHealthSimple()
                    System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Simple health check result: " & isHealthy.ToString())
                End If
            Catch healthEx As Exception
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Health check exception: " & healthEx.Message)
                isHealthy = False
            End Try

            If Not isHealthy Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Email service not healthy")
                ShowMessage("Sistem OTP tidak tersedia pada masa ini. Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
                Return
            End If

            ' Generate OTP
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Attempting to generate OTP")
            Dim response = emailServiceClient.GenerateOTP(userId, userEmail, "LOGIN")

            If response.Success Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: OTP generated successfully")
                ShowMessage("Kod OTP baharu telah dihantar ke email anda: " & MaskEmail(userEmail), "success")
            Else
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: OTP generation failed: " & response.Message)
                ShowMessage("Gagal menghantar OTP: " & response.Message & ". Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
                Return
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Exception during OTP generation: " & ex.Message)
            ShowMessage("Ralat sistem OTP: " & ex.Message & ". Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
            Return
        End Try
    End Sub

    Protected Sub ButtonVerifyOtp_Click(sender As Object, e As EventArgs) Handles ButtonVerifyOtp.Click
        Try
            If String.IsNullOrEmpty(TextBoxOtp.Text) OrElse TextBoxOtp.Text.Trim() = "" Then
                ShowMessage("Sila masukkan kod OTP", "danger")
                TextBoxOtp.Focus()
                Return
            End If

            If TextBoxOtp.Text.Length <> 6 OrElse Not IsNumeric(TextBoxOtp.Text) Then
                ShowMessage("Kod OTP mestilah 6 digit nombor", "danger")
                TextBoxOtp.Focus()
                Return
            End If

            Dim userId As String = Session("TEMP_USER_ID").ToString()
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Validating OTP for user: " & userId)

            ' Check if email service client is available
            If emailServiceClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Email service client not initialized, trying to reinitialize")
                Try
                    Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
                    If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
                    emailServiceClient = New EmailServiceClient(serviceUrl)
                Catch initEx As Exception
                    System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Failed to reinitialize email service client: " & initEx.Message)
                    ShowMessage("Sistem OTP tidak tersedia. Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
                    Return
                End Try
            End If

            ' Validate OTP
            Dim response = emailServiceClient.ValidateOTP(userId, TextBoxOtp.Text, "LOGIN")
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: OTP validation result: " & response.Success.ToString())

            If response.Success Then
                ' OTP is valid, complete login
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: OTP validation successful, completing login")
                CompleteLogin()
            Else
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: OTP validation failed: " & response.Message)
                ShowMessage(response.Message, "danger")
                TextBoxOtp.Text = ""
                TextBoxOtp.Focus()
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Exception during OTP validation: " & ex.Message)
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Protected Sub ButtonResendOtp_Click(sender As Object, e As EventArgs) Handles ButtonResendOtp.Click
        Try
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Resend OTP button clicked")

            ' Clear any existing message
            PanelMessage.Visible = False

            ' Check if email service client is available
            If emailServiceClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Email service client not initialized for resend, trying to reinitialize")
                Try
                    Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
                    If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
                    emailServiceClient = New EmailServiceClient(serviceUrl)
                Catch initEx As Exception
                    System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Failed to reinitialize email service client for resend: " & initEx.Message)
                    ShowMessage("Sistem OTP tidak tersedia. Sila cuba lagi atau hubungi pentadbir sistem.", "danger")
                    Return
                End Try
            End If

            ' Generate new OTP
            GenerateAndSendOtp()

            ' Clear OTP input
            TextBoxOtp.Text = ""
            TextBoxOtp.Focus()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("OTP VERIFICATION DEBUG: Exception during resend OTP: " & ex.Message)
            ShowMessage("Ralat sistem: " & ex.Message, "danger")
        End Try
    End Sub

    Private Sub CompleteLogin()
        Try
            ' Move temporary session data to permanent session
            Session("Id_PG") = Session("TEMP_USER_ID")
            Session("PWD") = ""
            Session("MODUL") = Session("TEMP_MODUL")
            Session("AKSES") = Session("TEMP_AKSES")
            Session("ORIGIN") = Session("TEMP_ORIGIN")

            ' Check if user needs to change password after OTP verification
            Dim forcePasswordChange As Boolean = False
            If Session("FORCE_PASSWORD_CHANGE") IsNot Nothing Then
                forcePasswordChange = Convert.ToBoolean(Session("FORCE_PASSWORD_CHANGE"))
            End If

            ' Clear temporary session data
            Session("TEMP_USER_ID") = Nothing
            Session("TEMP_MODUL") = Nothing
            Session("TEMP_AKSES") = Nothing
            Session("TEMP_ORIGIN") = Nothing

            ' Redirect based on whether password change is required
            If forcePasswordChange Then
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION: User needs password change after OTP verification")
                Response.Redirect("p0_PasswordChangeForced.aspx")
            Else
                System.Diagnostics.Debug.WriteLine("OTP VERIFICATION: Normal login completed after OTP verification")
                Response.Redirect("blank.aspx")
            End If

        Catch ex As Exception
            ShowMessage("Ralat melengkapkan log masuk: " & ex.Message, "danger")
        End Try
    End Sub

    Private Function GetUserEmail(userId As String) As String
        Try
            Dim email As String = ""
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = SPMJ_Mod.ServerId
            Cn.Open()
            Cmd.Connection = Cn
            Cmd.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ? AND status = '1'"
            Cmd.Parameters.AddWithValue("@id_pg", userId)
            
            Dim result = Cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                email = result.ToString().Trim()
            End If
            
            Cn.Close()
            Return email
        Catch
            Return ""
        End Try
    End Function

    Private Function MaskEmail(email As String) As String
        Try
            If String.IsNullOrEmpty(email) OrElse Not email.Contains("@") Then
                Return email
            End If

            Dim parts = email.Split("@"c)
            Dim localPart = parts(0)
            Dim domainPart = parts(1)

            If localPart.Length <= 2 Then
                Return email
            End If

            ' Show first and last character of local part, mask the middle
            Dim masked = localPart.Substring(0, 1) & 
                        New String("*"c, localPart.Length - 2) & 
                        localPart.Substring(localPart.Length - 1, 1) & 
                        "@" & domainPart

            Return masked
        Catch
            Return email
        End Try
    End Function

    Private Sub ShowMessage(message As String, type As String)
        LabelMessage.Text = message
        PanelMessage.Visible = True
        
        ' Set CSS class based on type
        Select Case type.ToLower()
            Case "success"
                alertDiv.Attributes("class") = "alert alert-success"
            Case "danger", "error"
                alertDiv.Attributes("class") = "alert alert-danger"
            Case "info"
                alertDiv.Attributes("class") = "alert alert-info"
            Case Else
                alertDiv.Attributes("class") = "alert alert-info"
        End Select
    End Sub

End Class
