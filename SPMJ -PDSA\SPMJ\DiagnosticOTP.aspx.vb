Imports System.Configuration
Imports System.Net

Partial Public Class DiagnosticOTP
    Inherits System.Web.UI.Page

    Private debugOutput As New System.Text.StringBuilder()

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            AddDebug("Diagnostic page loaded at " & DateTime.Now.ToString())
        End If
    End Sub

    Protected Sub btnTestConnectivity_Click(sender As Object, e As EventArgs) Handles btnTestConnectivity.Click
        AddDebug("=== CONNECTIVITY TEST ===")
        
        Try
            ' Test direct HTTP request to microservice
            Dim url As String = "http://localhost:5000/health"
            AddDebug("Testing URL: " & url)
            
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Method = "GET"
            request.Timeout = 5000
            
            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                AddDebug("Response Status: " & response.StatusCode.ToString())
                
                Using reader As New System.IO.StreamReader(response.GetResponseStream())
                    Dim responseText As String = reader.ReadToEnd()
                    AddDebug("Response Content: " & responseText)
                    
                    If response.StatusCode = HttpStatusCode.OK AndAlso responseText.Contains("healthy") Then
                        litConnectivityResult.Text = "<div class='success'>✅ Microservice is reachable and healthy</div>"
                    Else
                        litConnectivityResult.Text = "<div class='warning'>⚠️ Microservice responded but may not be healthy</div>"
                    End If
                End Using
            End Using
            
        Catch ex As Exception
            AddDebug("Connectivity test failed: " & ex.Message)
            litConnectivityResult.Text = "<div class='error'>❌ Cannot reach microservice: " & ex.Message & "</div>"
        End Try
        
        UpdateDebugOutput()
    End Sub

    Protected Sub btnTestInitialization_Click(sender As Object, e As EventArgs) Handles btnTestInitialization.Click
        AddDebug("=== INITIALIZATION TEST ===")
        
        Try
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            AddDebug("Service URL from config: " & serviceUrl)
            
            ' Test EmailServiceClient initialization
            Dim emailClient As EmailServiceClient = Nothing
            
            Try
                emailClient = New EmailServiceClient(serviceUrl)
                AddDebug("EmailServiceClient created successfully")
                
                ' Test connectivity method
                Dim canConnect As Boolean = emailClient.TestConnectivity()
                AddDebug("TestConnectivity result: " & canConnect.ToString())
                
                If canConnect Then
                    litInitResult.Text = "<div class='success'>✅ EmailServiceClient initialized and can connect</div>"
                Else
                    litInitResult.Text = "<div class='warning'>⚠️ EmailServiceClient initialized but cannot connect</div>"
                End If
                
            Catch initEx As Exception
                AddDebug("EmailServiceClient initialization failed: " & initEx.Message)
                litInitResult.Text = "<div class='error'>❌ EmailServiceClient initialization failed: " & initEx.Message & "</div>"
            End Try
            
        Catch ex As Exception
            AddDebug("Initialization test failed: " & ex.Message)
            litInitResult.Text = "<div class='error'>❌ Initialization test failed: " & ex.Message & "</div>"
        End Try
        
        UpdateDebugOutput()
    End Sub

    Protected Sub btnTestHealthChecks_Click(sender As Object, e As EventArgs) Handles btnTestHealthChecks.Click
        AddDebug("=== HEALTH CHECK TEST ===")
        
        Try
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            AddDebug("Testing health check methods...")
            
            ' Test primary health check
            Dim primaryHealth As Boolean = emailClient.CheckHealth()
            AddDebug("Primary CheckHealth() result: " & primaryHealth.ToString())
            
            ' Test simple health check
            Dim simpleHealth As Boolean = emailClient.CheckHealthSimple()
            AddDebug("Simple CheckHealthSimple() result: " & simpleHealth.ToString())
            
            If primaryHealth OrElse simpleHealth Then
                litHealthResult.Text = "<div class='success'>✅ At least one health check method works</div>"
            Else
                litHealthResult.Text = "<div class='error'>❌ Both health check methods failed</div>"
            End If
            
        Catch ex As Exception
            AddDebug("Health check test failed: " & ex.Message)
            litHealthResult.Text = "<div class='error'>❌ Health check test failed: " & ex.Message & "</div>"
        End Try
        
        UpdateDebugOutput()
    End Sub

    Protected Sub btnTestConfig_Click(sender As Object, e As EventArgs) Handles btnTestConfig.Click
        AddDebug("=== CONFIGURATION TEST ===")
        
        Try
            ' Check configuration values
            Dim emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            Dim emailServiceEnabled As String = ConfigurationManager.AppSettings("EmailServiceEnabled")
            Dim otpEnabled As String = ConfigurationManager.AppSettings("OtpEnabled")
            
            AddDebug("EmailServiceUrl: " & If(emailServiceUrl, "NOT SET"))
            AddDebug("EmailServiceEnabled: " & If(emailServiceEnabled, "NOT SET"))
            AddDebug("OtpEnabled: " & If(otpEnabled, "NOT SET"))
            
            Dim configOk As Boolean = True
            Dim configMessage As String = ""
            
            If String.IsNullOrEmpty(emailServiceUrl) Then
                configMessage += "EmailServiceUrl not configured. "
                configOk = False
            End If
            
            If String.IsNullOrEmpty(otpEnabled) OrElse otpEnabled.ToLower() <> "true" Then
                configMessage += "OtpEnabled not set to true. "
                configOk = False
            End If
            
            If configOk Then
                litConfigResult.Text = "<div class='success'>✅ Configuration looks good</div>"
            Else
                litConfigResult.Text = "<div class='warning'>⚠️ Configuration issues: " & configMessage & "</div>"
            End If
            
        Catch ex As Exception
            AddDebug("Configuration test failed: " & ex.Message)
            litConfigResult.Text = "<div class='error'>❌ Configuration test failed: " & ex.Message & "</div>"
        End Try
        
        UpdateDebugOutput()
    End Sub

    Private Sub AddDebug(message As String)
        debugOutput.AppendLine(DateTime.Now.ToString("HH:mm:ss.fff") & " - " & message)
    End Sub

    Private Sub UpdateDebugOutput()
        litDebugOutput.Text = debugOutput.ToString()
    End Sub

End Class
