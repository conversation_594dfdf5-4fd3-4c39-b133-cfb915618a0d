Imports System.Data.OleDb
Imports System.Text

Partial Public Class TestLoginFlow
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            UpdateSummary("Page loaded successfully")
        End If
    End Sub

    Protected Sub btnTestDatabase_Click(sender As Object, e As EventArgs) Handles btnTestDatabase.Click
        Try
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()
                
                Dim result As New StringBuilder()
                result.AppendLine("<div class='success'>✅ Database connection successful</div>")
                result.AppendLine("<pre>")
                result.AppendLine("Connection String: " & Server.HtmlEncode(SPMJ_Mod.ServerId))
                result.AppendLine("Database State: " & connection.State.ToString())
                result.AppendLine("</pre>")
                
                ' Test table access
                Using command As New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna", connection)
                    Dim userCount As Integer = Convert.ToInt32(command.ExecuteScalar())
                    result.AppendLine("<div class='info'>Total users in database: " & userCount & "</div>")
                End Using
                
                connection.Close()
                litDatabaseResult.Text = result.ToString()
                UpdateSummary("Database test: SUCCESS")
                
            End Using
        Catch ex As Exception
            litDatabaseResult.Text = "<div class='error'>❌ Database connection failed: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("Database test: FAILED - " & ex.Message)
        End Try
    End Sub

    Protected Sub btnLookupUser_Click(sender As Object, e As EventArgs) Handles btnLookupUser.Click
        If String.IsNullOrEmpty(txtUserId.Text) Then
            litUserResult.Text = "<div class='error'>Please enter a User ID</div>"
            Return
        End If
        
        Try
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()
                
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT id_pg, nama, email, pwd, salt, password_migrated FROM pn_pengguna WHERE id_pg = ?"
                    command.Parameters.AddWithValue("", txtUserId.Text)
                    
                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            Dim result As New StringBuilder()
                            result.AppendLine("<div class='success'>✅ User found</div>")
                            result.AppendLine("<pre>")
                            result.AppendLine("ID: " & If(reader("id_pg") Is DBNull.Value, "NULL", reader("id_pg").ToString()))
                            result.AppendLine("Name: " & If(reader("nama") Is DBNull.Value, "NULL", reader("nama").ToString()))
                            result.AppendLine("Email: " & If(reader("email") Is DBNull.Value, "NULL", reader("email").ToString()))
                            result.AppendLine("Password Length: " & If(reader("pwd") Is DBNull.Value, 0, reader("pwd").ToString().Length))
                            result.AppendLine("Salt Length: " & If(reader("salt") Is DBNull.Value, 0, reader("salt").ToString().Length))
                            result.AppendLine("Password Migrated: " & If(reader("password_migrated") Is DBNull.Value, "NULL", reader("password_migrated").ToString()))
                            result.AppendLine("</pre>")
                            
                            litUserResult.Text = result.ToString()
                            UpdateSummary("User lookup: FOUND - " & txtUserId.Text)
                        Else
                            litUserResult.Text = "<div class='error'>❌ User not found</div>"
                            UpdateSummary("User lookup: NOT FOUND - " & txtUserId.Text)
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            litUserResult.Text = "<div class='error'>❌ User lookup error: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("User lookup: ERROR - " & ex.Message)
        End Try
    End Sub

    Protected Sub btnTestPassword_Click(sender As Object, e As EventArgs) Handles btnTestPassword.Click
        If String.IsNullOrEmpty(txtTestUserId.Text) OrElse String.IsNullOrEmpty(txtTestPassword.Text) Then
            litPasswordResult.Text = "<div class='error'>Please enter both User ID and Password</div>"
            Return
        End If
        
        Try
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()
                
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT pwd, salt, password_migrated FROM pn_pengguna WHERE id_pg = ?"
                    command.Parameters.AddWithValue("", txtTestUserId.Text)
                    
                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            Dim storedPassword As String = If(reader("pwd") Is DBNull.Value, "", reader("pwd").ToString())
                            Dim salt As String = If(reader("salt") Is DBNull.Value, "", reader("salt").ToString())
                            Dim isMigrated As Boolean = If(reader("password_migrated") Is DBNull.Value, False, Convert.ToBoolean(reader("password_migrated")))
                            
                            Dim result As New StringBuilder()
                            result.AppendLine("<h4>Password Verification Test:</h4>")
                            result.AppendLine("<pre>")
                            
                            ' Test different verification methods
                            Dim passwordValid As Boolean = False
                            
                            If Not String.IsNullOrEmpty(salt) AndAlso isMigrated Then
                                ' Encrypted password verification
                                Try
                                    passwordValid = PasswordHelper.VerifyPassword(txtTestPassword.Text, storedPassword, salt)
                                    result.AppendLine("Verification Method: ENCRYPTED")
                                    result.AppendLine("Result: " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                                Catch ex As Exception
                                    result.AppendLine("Verification Method: ENCRYPTED")
                                    result.AppendLine("Result: ❌ ERROR - " & ex.Message)
                                End Try
                            Else
                                ' Plain text password verification
                                passwordValid = storedPassword.Equals(txtTestPassword.Text)
                                result.AppendLine("Verification Method: PLAIN TEXT")
                                result.AppendLine("Result: " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                            End If
                            
                            result.AppendLine("</pre>")
                            litPasswordResult.Text = result.ToString()
                            UpdateSummary("Password test: " & If(passwordValid, "VALID", "INVALID") & " - " & txtTestUserId.Text)
                        Else
                            litPasswordResult.Text = "<div class='error'>❌ User not found</div>"
                            UpdateSummary("Password test: USER NOT FOUND - " & txtTestUserId.Text)
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            litPasswordResult.Text = "<div class='error'>❌ Password test error: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("Password test: ERROR - " & ex.Message)
        End Try
    End Sub

    Protected Sub btnCheckOtpReq_Click(sender As Object, e As EventArgs) Handles btnCheckOtpReq.Click
        If String.IsNullOrEmpty(txtOtpUserId.Text) Then
            litOtpReqResult.Text = "<div class='error'>Please enter a User ID</div>"
            Return
        End If
        
        Try
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()
                
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT email, salt, password_migrated FROM pn_pengguna WHERE id_pg = ?"
                    command.Parameters.AddWithValue("", txtOtpUserId.Text)
                    
                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            Dim email As String = If(reader("email") Is DBNull.Value, "", reader("email").ToString())
                            Dim salt As String = If(reader("salt") Is DBNull.Value, "", reader("salt").ToString())
                            Dim isMigrated As Boolean = If(reader("password_migrated") Is DBNull.Value, False, Convert.ToBoolean(reader("password_migrated")))
                            
                            Dim result As New StringBuilder()
                            result.AppendLine("<h4>OTP Requirement Analysis:</h4>")
                            result.AppendLine("<pre>")
                            result.AppendLine("Email: '" & Server.HtmlEncode(email) & "'")
                            result.AppendLine("Has Salt: " & (Not String.IsNullOrEmpty(salt)).ToString())
                            result.AppendLine("Password Migrated: " & isMigrated.ToString())
                            result.AppendLine("")
                            
                            ' Check OTP requirements
                            Dim hasValidEmail As Boolean = Not String.IsNullOrEmpty(email) AndAlso email.Contains("@")
                            Dim hasEncryptedPassword As Boolean = Not String.IsNullOrEmpty(salt) OrElse isMigrated
                            Dim otpRequired As Boolean = hasValidEmail AndAlso hasEncryptedPassword
                            
                            result.AppendLine("Has Valid Email: " & hasValidEmail.ToString())
                            result.AppendLine("Has Encrypted Password: " & hasEncryptedPassword.ToString())
                            result.AppendLine("OTP REQUIRED: " & If(otpRequired, "✅ YES", "❌ NO"))
                            result.AppendLine("</pre>")
                            
                            litOtpReqResult.Text = result.ToString()
                            UpdateSummary("OTP check: " & If(otpRequired, "REQUIRED", "NOT REQUIRED") & " - " & txtOtpUserId.Text)
                        Else
                            litOtpReqResult.Text = "<div class='error'>❌ User not found</div>"
                            UpdateSummary("OTP check: USER NOT FOUND - " & txtOtpUserId.Text)
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            litOtpReqResult.Text = "<div class='error'>❌ OTP requirement check error: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("OTP check: ERROR - " & ex.Message)
        End Try
    End Sub

    Protected Sub btnCreateUser_Click(sender As Object, e As EventArgs) Handles btnCreateUser.Click
        If String.IsNullOrEmpty(txtNewUserId.Text) OrElse String.IsNullOrEmpty(txtNewUserPassword.Text) Then
            litCreateUserResult.Text = "<div class='error'>Please enter User ID and Password</div>"
            Return
        End If

        Try
            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()

                ' Check if user already exists
                Using checkCommand As New OleDbCommand()
                    checkCommand.Connection = connection
                    checkCommand.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE id_pg = ?"
                    checkCommand.Parameters.AddWithValue("", txtNewUserId.Text)

                    Dim userExists As Integer = Convert.ToInt32(checkCommand.ExecuteScalar())
                    If userExists > 0 Then
                        litCreateUserResult.Text = "<div class='warning'>⚠️ User already exists. Use a different ID.</div>"
                        Return
                    End If
                End Using

                ' Create new user
                Using command As New OleDbCommand()
                    command.Connection = connection

                    If chkEncrypted.Checked Then
                        ' Create with encrypted password
                        Dim salt As String = PasswordHelper.GenerateSalt()
                        Dim hashedPassword As String = PasswordHelper.HashPassword(txtNewUserPassword.Text, salt)

                        command.CommandText = "INSERT INTO pn_pengguna (id_pg, nama, email, pwd, salt, password_migrated, status) VALUES (?, ?, ?, ?, ?, ?, ?)"
                        command.Parameters.AddWithValue("", txtNewUserId.Text)
                        command.Parameters.AddWithValue("", txtNewUserName.Text)
                        command.Parameters.AddWithValue("", txtNewUserEmail.Text)
                        command.Parameters.AddWithValue("", hashedPassword)
                        command.Parameters.AddWithValue("", salt)
                        command.Parameters.AddWithValue("", True)
                        command.Parameters.AddWithValue("", 1)
                    Else
                        ' Create with plain text password
                        command.CommandText = "INSERT INTO pn_pengguna (id_pg, nama, email, pwd, status) VALUES (?, ?, ?, ?, ?)"
                        command.Parameters.AddWithValue("", txtNewUserId.Text)
                        command.Parameters.AddWithValue("", txtNewUserName.Text)
                        command.Parameters.AddWithValue("", txtNewUserEmail.Text)
                        command.Parameters.AddWithValue("", txtNewUserPassword.Text)
                        command.Parameters.AddWithValue("", 1)
                    End If

                    command.ExecuteNonQuery()

                    litCreateUserResult.Text = "<div class='success'>✅ Test user created successfully</div>"
                    UpdateSummary("User created: " & txtNewUserId.Text & " (Encrypted: " & chkEncrypted.Checked.ToString() & ")")
                End Using
            End Using
        Catch ex As Exception
            litCreateUserResult.Text = "<div class='error'>❌ User creation error: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("User creation: ERROR - " & ex.Message)
        End Try
    End Sub

    Protected Sub btnSimulateLogin_Click(sender As Object, e As EventArgs) Handles btnSimulateLogin.Click
        If String.IsNullOrEmpty(txtLoginUserId.Text) OrElse String.IsNullOrEmpty(txtLoginPassword.Text) Then
            litLoginResult.Text = "<div class='error'>Please enter both User ID and Password</div>"
            Return
        End If

        Try
            Dim result As New StringBuilder()
            result.AppendLine("<h4>Login Simulation Results:</h4>")
            result.AppendLine("<pre>")

            Using connection As New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()

                ' Step 1: User lookup
                Using command As New OleDbCommand()
                    command.Connection = connection
                    command.CommandText = "SELECT pwd, salt, password_migrated, email FROM pn_pengguna WHERE id_pg = ? AND status = 1"
                    command.Parameters.AddWithValue("", txtLoginUserId.Text)

                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            result.AppendLine("Step 1: User lookup - ✅ SUCCESS")

                            Dim storedPassword As String = If(reader("pwd") Is DBNull.Value, "", reader("pwd").ToString())
                            Dim salt As String = If(reader("salt") Is DBNull.Value, "", reader("salt").ToString())
                            Dim isMigrated As Boolean = If(reader("password_migrated") Is DBNull.Value, False, Convert.ToBoolean(reader("password_migrated")))
                            Dim email As String = If(reader("email") Is DBNull.Value, "", reader("email").ToString())

                            ' Step 2: Password verification
                            Dim passwordValid As Boolean = False
                            Dim needsMigration As Boolean = False

                            If Not String.IsNullOrEmpty(salt) AndAlso isMigrated Then
                                ' Encrypted password
                                passwordValid = PasswordHelper.VerifyPassword(txtLoginPassword.Text, storedPassword, salt)
                                result.AppendLine("Step 2: Password verification (encrypted) - " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                            Else
                                ' Plain text password
                                passwordValid = storedPassword.Equals(txtLoginPassword.Text)
                                needsMigration = passwordValid
                                result.AppendLine("Step 2: Password verification (plain) - " & If(passwordValid, "✅ VALID", "❌ INVALID"))
                                If needsMigration Then
                                    result.AppendLine("         Password migration needed - ⚠️ YES")
                                End If
                            End If

                            If passwordValid Then
                                ' Step 3: OTP requirement check
                                Dim hasValidEmail As Boolean = Not String.IsNullOrEmpty(email) AndAlso email.Contains("@")
                                Dim hasEncryptedPassword As Boolean = Not String.IsNullOrEmpty(salt) OrElse isMigrated
                                Dim otpRequired As Boolean = hasValidEmail AndAlso (hasEncryptedPassword OrElse needsMigration)

                                result.AppendLine("Step 3: OTP requirement check - " & If(otpRequired, "✅ REQUIRED", "❌ NOT REQUIRED"))

                                ' Step 4: Determine next action
                                If needsMigration Then
                                    result.AppendLine("Step 4: Next action - 🔄 MIGRATE PASSWORD")
                                    If otpRequired Then
                                        result.AppendLine("         Then redirect to - 🔐 OTP VERIFICATION")
                                        result.AppendLine("         Finally redirect to - 🔑 PASSWORD CHANGE")
                                    Else
                                        result.AppendLine("         Then redirect to - 🔑 PASSWORD CHANGE")
                                    End If
                                ElseIf otpRequired Then
                                    result.AppendLine("Step 4: Next action - 🔐 OTP VERIFICATION")
                                    result.AppendLine("         Then redirect to - 🏠 MAIN APPLICATION")
                                Else
                                    result.AppendLine("Step 4: Next action - 🏠 DIRECT LOGIN TO MAIN APPLICATION")
                                End If

                                result.AppendLine("")
                                result.AppendLine("LOGIN SIMULATION: ✅ SUCCESS")
                            Else
                                result.AppendLine("Step 3: LOGIN SIMULATION: ❌ FAILED (Invalid password)")
                            End If

                        Else
                            result.AppendLine("Step 1: User lookup - ❌ FAILED (User not found or inactive)")
                            result.AppendLine("LOGIN SIMULATION: ❌ FAILED")
                        End If
                    End Using
                End Using
            End Using

            result.AppendLine("</pre>")
            litLoginResult.Text = result.ToString()
            UpdateSummary("Login simulation: " & txtLoginUserId.Text)

        Catch ex As Exception
            litLoginResult.Text = "<div class='error'>❌ Login simulation error: " & Server.HtmlEncode(ex.Message) & "</div>"
            UpdateSummary("Login simulation: ERROR - " & ex.Message)
        End Try
    End Sub

    Private Sub UpdateSummary(message As String)
        Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
        Dim currentSummary As String = If(litSummary.Text, "")
        litSummary.Text = currentSummary & "[" & timestamp & "] " & message & "<br/>"
    End Sub

End Class
