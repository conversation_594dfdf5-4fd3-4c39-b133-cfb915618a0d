Imports System.Data.OleDb
Imports System.Configuration

Partial Public Class QuickTestKolejLogin
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            spanUserId.InnerText = txtQuickUserId.Text
            spanEmail.InnerText = txtQuickEmail.Text
        End If
    End Sub

    Protected Sub btnQuickCheck_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnQuickCheck_Click
        Dim result As String = ""
        Dim allGood As Boolean = True
        
        Try
            result += "<h4>🔍 System Status Check Results:</h4>"
            
            ' 1. Check OTP Microservice
            Try
                Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
                If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
                
                Dim emailClient As New EmailServiceClient(serviceUrl)
                Dim isHealthy As Boolean = emailClient.CheckHealth()
                
                If isHealthy Then
                    result += "✅ <span class='status-good'>OTP Microservice: HEALTHY</span><br/>"
                Else
                    result += "❌ <span class='status-bad'>OTP Microservice: UNHEALTHY</span><br/>"
                    allGood = False
                End If
            Catch ex As Exception
                result += "❌ <span class='status-bad'>OTP Microservice: ERROR - " & ex.Message & "</span><br/>"
                allGood = False
            End Try
            
            ' 2. Check Database Connection
            Try
                Dim Cn As New OleDbConnection
                Cn.ConnectionString = ServerId
                Cn.Open()
                
                result += "✅ <span class='status-good'>Database Connection: OK</span><br/>"
                
                ' Check required columns
                Dim requiredColumns As String() = {"email", "salt", "pwd_encrypted"}
                For Each column As String In requiredColumns
                    Try
                        Dim Cmd As New OleDbCommand
                        Cmd.Connection = Cn
                        Cmd.CommandText = "SELECT TOP 1 " & column & " FROM kj_pengguna"
                        Cmd.ExecuteScalar()
                        result += "✅ <span class='status-good'>Column '" & column & "': EXISTS</span><br/>"
                    Catch
                        result += "❌ <span class='status-bad'>Column '" & column & "': MISSING</span><br/>"
                        allGood = False
                    End Try
                Next
                
                Cn.Close()
            Catch ex As Exception
                result += "❌ <span class='status-bad'>Database Connection: ERROR - " & ex.Message & "</span><br/>"
                allGood = False
            End Try
            
            ' 3. Overall Status
            result += "<br/><h4>📊 Overall System Status:</h4>"
            If allGood Then
                result += "🎉 <span class='status-good'>ALL SYSTEMS READY FOR KOLEJ-PDSA ENHANCED LOGIN!</span><br/>"
                result += "<p>✅ You can now test the enhanced login functionality with encrypted passwords and OTP verification.</p>"
            Else
                result += "⚠️ <span class='status-warning'>SOME ISSUES DETECTED - Please resolve before testing</span><br/>"
            End If
            
            lblQuickCheckResult.Text = result
            
        Catch ex As Exception
            lblQuickCheckResult.Text = "❌ <span class='status-bad'>System Check Failed: " & ex.Message & "</span>"
        End Try
    End Sub

    Protected Sub btnCreateTestUser_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCreateTestUser.Click
        Try
            lblTestUserResult.Text = "Creating test KOLEJ user...<br/>"
            
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand
            
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Check if user exists
            Cmd.CommandText = "SELECT COUNT(*) FROM kj_pengguna WHERE Id_PG = ?"
            Cmd.Parameters.Clear()
            Cmd.Parameters.AddWithValue("@Id_PG", txtQuickUserId.Text.Trim())
            
            Dim userExists As Integer = CInt(Cmd.ExecuteScalar())
            
            If userExists > 0 Then
                ' Update existing user
                Cmd.CommandText = "UPDATE kj_pengguna SET email = ?, pwd_encrypted = 1 WHERE Id_PG = ?"
                Cmd.Parameters.Clear()
                Cmd.Parameters.AddWithValue("@email", txtQuickEmail.Text.Trim())
                Cmd.Parameters.AddWithValue("@Id_PG", txtQuickUserId.Text.Trim())
                
                Cmd.ExecuteNonQuery()
                
                lblTestUserResult.Text += "✅ <span class='status-good'>Existing user updated with email and encryption flag</span><br/>"
            Else
                ' Create new user with encrypted password
                Dim passwordHelper As New PasswordHelper()
                Dim salt As String = passwordHelper.GenerateSalt()
                Dim hashedPassword As String = passwordHelper.HashPassword("kolej123", salt)
                
                Cmd.CommandText = "INSERT INTO kj_pengguna (Id_PG, PWD, salt, pwd_encrypted, email, NAMA, STATUS, Id_KOLEJ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                Cmd.Parameters.Clear()
                Cmd.Parameters.AddWithValue("@Id_PG", txtQuickUserId.Text.Trim())
                Cmd.Parameters.AddWithValue("@PWD", hashedPassword)
                Cmd.Parameters.AddWithValue("@salt", salt)
                Cmd.Parameters.AddWithValue("@pwd_encrypted", True)
                Cmd.Parameters.AddWithValue("@email", txtQuickEmail.Text.Trim())
                Cmd.Parameters.AddWithValue("@NAMA", "Test KOLEJ User")
                Cmd.Parameters.AddWithValue("@STATUS", 1)
                Cmd.Parameters.AddWithValue("@Id_KOLEJ", "K001")
                
                Cmd.ExecuteNonQuery()
                
                lblTestUserResult.Text += "✅ <span class='status-good'>New test user created successfully!</span><br/>"
            End If
            
            lblTestUserResult.Text += "<br/><strong>Test User Details:</strong><br/>"
            lblTestUserResult.Text += "• User ID: " & txtQuickUserId.Text & "<br/>"
            lblTestUserResult.Text += "• Password: kolej123<br/>"
            lblTestUserResult.Text += "• Email: " & txtQuickEmail.Text & "<br/>"
            lblTestUserResult.Text += "• Password Type: Encrypted (will trigger OTP)<br/>"
            lblTestUserResult.Text += "<br/>🎯 <span class='status-good'>Ready for testing enhanced login flow!</span>"
            
            ' Update display
            spanUserId.InnerText = txtQuickUserId.Text
            spanEmail.InnerText = txtQuickEmail.Text
            
            Cn.Close()
            
        Catch ex As Exception
            lblTestUserResult.Text = "❌ <span class='status-bad'>Error creating test user: " & ex.Message & "</span>"
        End Try
    End Sub

    Protected Sub btnTestOtpFlow_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnTestOtpFlow.Click
        Try
            lblOtpFlowResult.Text = "Testing OTP flow for KOLEJ system...<br/>"
            
            Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
            
            Dim emailClient As New EmailServiceClient(serviceUrl)
            
            ' Test OTP generation
            Dim response = emailClient.GenerateOTP(txtQuickUserId.Text, txtQuickEmail.Text, "LOGIN_KOLEJ_SECURE")
            
            lblOtpFlowResult.Text += "<strong>OTP Generation Test:</strong><br/>"
            lblOtpFlowResult.Text += "• Success: " & response.Success.ToString() & "<br/>"
            lblOtpFlowResult.Text += "• Message: " & response.Message & "<br/>"
            
            If response.Success Then
                lblOtpFlowResult.Text += "✅ <span class='status-good'>OTP generated successfully!</span><br/>"
                lblOtpFlowResult.Text += "<br/>📧 <span class='status-good'>Check email for OTP code</span><br/>"
                lblOtpFlowResult.Text += "<br/>🔄 <span class='status-warning'>Next: Use Login_J.aspx to test complete flow</span>"
            Else
                lblOtpFlowResult.Text += "❌ <span class='status-bad'>OTP generation failed</span><br/>"
            End If
            
        Catch ex As Exception
            lblOtpFlowResult.Text = "❌ <span class='status-bad'>Error testing OTP flow: " & ex.Message & "</span>"
        End Try
    End Sub

    Protected Sub btnGoToKolejLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToKolejLogin.Click
        Response.Redirect("Login_J.aspx")
    End Sub

    Protected Sub btnGoToFullTest_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnGoToFullTest.Click
        Response.Redirect("TestKolejLoginEnhancements.aspx")
    End Sub
End Class
