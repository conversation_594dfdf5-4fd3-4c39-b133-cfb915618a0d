Partial Public Class TestPasswordRecoveryToggle
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            lbl_TestResults.Text = "Click the 'Lupa Kata Laluan?' link to test toggle functionality."
        End If
    End Sub

    Protected Sub lnk_TestForgotPassword_Click(sender As Object, e As EventArgs) Handles lnk_TestForgotPassword.Click
        ' This simulates the exact logic from p0_Login.aspx.vb
        System.Diagnostics.Debug.WriteLine("TEST: Forgot password link clicked")

        ' Toggle the password recovery panel visibility
        If pnl_TestPasswordRecovery.Visible Then
            ' Hide the password recovery panel
            pnl_TestPasswordRecovery.Visible = False
            lbl_TestRecoveryMessage.Text = ""
            txt_TestRecoveryUserId.Text = ""
            lbl_TestResults.Text = "<span class='success'>✅ Panel hidden successfully</span><br/>" & 
                                  "Panel was visible, now it's hidden. Click again to show it."
            System.Diagnostics.Debug.WriteLine("TEST: Recovery panel hidden")
        Else
            ' Show the password recovery panel
            pnl_TestPasswordRecovery.Visible = True
            lbl_TestRecoveryMessage.Text = ""
            txt_TestRecoveryUserId.Text = ""
            txt_TestRecoveryUserId.Focus()
            lbl_TestResults.Text = "<span class='success'>✅ Panel shown successfully</span><br/>" & 
                                  "Panel was hidden, now it's visible. Click again to hide it."
            System.Diagnostics.Debug.WriteLine("TEST: Recovery panel displayed")
        End If
    End Sub

    Protected Sub btn_TestCancelRecovery_Click(sender As Object, e As EventArgs) Handles btn_TestCancelRecovery.Click
        ' This simulates the cancel functionality
        System.Diagnostics.Debug.WriteLine("TEST: Cancel recovery button clicked")

        ' Hide the password recovery panel
        pnl_TestPasswordRecovery.Visible = False
        lbl_TestRecoveryMessage.Text = ""
        txt_TestRecoveryUserId.Text = ""
        
        lbl_TestResults.Text = "<span class='success'>✅ Cancel button works correctly</span><br/>" & 
                              "Panel hidden via cancel button. Click 'Lupa Kata Laluan?' to show it again."

        System.Diagnostics.Debug.WriteLine("TEST: Recovery panel hidden via cancel")
    End Sub

    Protected Sub btn_GoToLogin_Click(sender As Object, e As EventArgs) Handles btn_GoToLogin.Click
        Response.Redirect("p0_Login.aspx")
    End Sub
End Class
