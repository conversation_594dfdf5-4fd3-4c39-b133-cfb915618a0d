<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="MockOtpService.aspx.vb" Inherits="SPMJ.MockOtpService" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Mock OTP Service</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ccc; background: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #eee; padding: 10px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>🔧 Mock OTP Service for Testing</h1>
        <p>This service simulates the OTP microservice for testing purposes.</p>
        
        <div class="endpoint">
            <h3>Health Check</h3>
            <p><strong>GET</strong> /health</p>
            <asp:Button ID="btnTestHealth" runat="server" Text="Test Health Endpoint" />
            <div id="healthResult">
                <asp:Literal ID="litHealthResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="endpoint">
            <h3>Generate OTP</h3>
            <p><strong>POST</strong> /api/otp/generate</p>
            <p>User ID: <asp:TextBox ID="txtUserId" runat="server" Text="testuser001"></asp:TextBox></p>
            <p>Email: <asp:TextBox ID="txtEmail" runat="server" Text="<EMAIL>"></asp:TextBox></p>
            <p>Purpose: <asp:TextBox ID="txtPurpose" runat="server" Text="LOGIN"></asp:TextBox></p>
            <asp:Button ID="btnGenerateOtp" runat="server" Text="Generate OTP" />
            <div id="generateResult">
                <asp:Literal ID="litGenerateResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="endpoint">
            <h3>Validate OTP</h3>
            <p><strong>POST</strong> /api/otp/validate</p>
            <p>User ID: <asp:TextBox ID="txtValidateUserId" runat="server" Text="testuser001"></asp:TextBox></p>
            <p>OTP Code: <asp:TextBox ID="txtOtpCode" runat="server" Text="123456"></asp:TextBox></p>
            <p>Purpose: <asp:TextBox ID="txtValidatePurpose" runat="server" Text="LOGIN"></asp:TextBox></p>
            <asp:Button ID="btnValidateOtp" runat="server" Text="Validate OTP" />
            <div id="validateResult">
                <asp:Literal ID="litValidateResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="endpoint">
            <h3>Test EmailServiceClient Integration</h3>
            <p>Test the .NET 3.5 EmailServiceClient against this mock service</p>
            <asp:Button ID="btnTestClient" runat="server" Text="Test EmailServiceClient" />
            <div id="clientResult">
                <asp:Literal ID="litClientResult" runat="server"></asp:Literal>
            </div>
        </div>
        
        <div class="endpoint">
            <h3>Mock Service Log</h3>
            <asp:Literal ID="litLog" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
